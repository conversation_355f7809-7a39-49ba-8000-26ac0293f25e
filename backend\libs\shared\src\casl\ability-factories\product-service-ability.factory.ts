import {
  AbilityBuilder,
  ExtractSubjectType,
  MongoAbility,
  createMongoAbility,
} from '@casl/ability';
import { Injectable } from '@nestjs/common';
import { ApiConsumer } from '@shared/shared/modules/auth/interfaces';

type Action = 'create' | 'list' | 'edit' | 'delete';

type Subjects = 'ProductService' | 'all';

export type ProductServiceAbility = MongoAbility<[Action, Subjects]>;

@Injectable()
export class ProductServiceAbilityFactory {
  constructor() {}

  async createForAPIConsumer(apiConsumer: ApiConsumer) {
    const { can, build } = new AbilityBuilder(createMongoAbility);

    // Get user permissions from token
    const userPermissions = apiConsumer.permissions || [];

    // Define permissions based on token permissions

    // product_service:list permission
    if (userPermissions.includes('product_service:list')) {
      can('list', 'ProductService');
    }

    // product_service:edit permission
    if (userPermissions.includes('product_service:edit')) {
      can('edit', 'ProductService');
    }

    return build({
      detectSubjectType: (item: any) =>
        item.constructor as ExtractSubjectType<Subjects>,
    });
  }
}
