import {
  AbilityBuilder,
  ExtractSubjectType,
  MongoAbility,
  createMongoAbility,
} from '@casl/ability';
import { Injectable } from '@nestjs/common';
import { ApiConsumer } from '@shared/shared/modules/auth/interfaces';

type Action = 'create' | 'list' | 'edit' | 'delete' | 'manage_permissions';

type Subjects = 'Role' | 'all';

export type RolesAbility = MongoAbility<[Action, Subjects]>;

@Injectable()
export class RolesAbilityFactory {
  constructor() {}

  async createForAPIConsumer(apiConsumer: ApiConsumer) {
    const { can, build } = new AbilityBuilder(createMongoAbility);

    // Get user permissions from token
    const userPermissions = apiConsumer.permissions || [];

    // Define permissions based on token permissions

    // roles:create permission
    if (userPermissions.includes('roles:create')) {
      can('create', 'Role');
    }

    // roles:list permission
    if (userPermissions.includes('roles:list')) {
      can('list', 'Role');
    }

    // roles:edit permission
    if (userPermissions.includes('roles:edit')) {
      can('edit', 'Role');
    }

    // roles:manage_permissions permission
    if (userPermissions.includes('roles:manage_permissions')) {
      can('manage_permissions', 'Role');
    }

    return build({
      detectSubjectType: (item: any) =>
        item.constructor as ExtractSubjectType<Subjects>,
    });
  }
}
