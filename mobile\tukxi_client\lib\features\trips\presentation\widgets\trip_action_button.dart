import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/theme/app_colors.dart';

class TripActionButton extends StatelessWidget {
  const TripActionButton({
    super.key,
    required this.itemHeight,
    required this.image,
    required this.label,
    required this.onTap,
  });

  final double itemHeight;
  final String image;
  final String label;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Container(
        height: itemHeight,
        padding: const EdgeInsets.only(left: 7, right: 10),
        decoration: BoxDecoration(
          color: AppColors.greyBg,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(image, height: 28),
            SizedBox(width: 5),
            Text(
              label,
              style: GoogleFonts.inter(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: AppColors.black50,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
