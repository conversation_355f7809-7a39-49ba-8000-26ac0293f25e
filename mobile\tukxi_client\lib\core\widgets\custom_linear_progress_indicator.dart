import 'package:flutter/material.dart';

class CustomLinearProgressIndicator extends StatefulWidget {
  const CustomLinearProgressIndicator({
    super.key,
    required this.borderRadius,
    this.backgroundColor,
    this.minHeight = 8,
    this.color,
  });

  final BorderRadius? borderRadius;
  final double minHeight;
  final Color? backgroundColor;
  final Color? color;

  @override
  State<CustomLinearProgressIndicator> createState() =>
      _CustomLinearProgressIndicatorState();
}

class _CustomLinearProgressIndicatorState
    extends State<CustomLinearProgressIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 5),
    )..repeat();
    //..repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return LinearProgressIndicator(
          value: _controller.value, // controlled progress
          minHeight: widget.minHeight,
          backgroundColor: widget.backgroundColor ?? Colors.grey[300],
          color: widget.color ?? Colors.blue,
          borderRadius: widget.borderRadius,
        );
      },
    );
  }
}
