import 'package:tukxi/features/location_selection/domain/entities/place_suggestion_entity.dart';

class PlaceSuggestion extends PlaceSuggestionEntity {
  final double? distanceMeters;

  PlaceSuggestion({
    required super.placeId,
    required super.name,
    required super.address,
    this.distanceMeters,
  });

  PlaceSuggestion copyWith({
    String? placeId,
    String? name,
    String? address,
    double? distanceMeters,
  }) {
    return PlaceSuggestion(
      placeId: placeId ?? this.placeId,
      name: name ?? this.name,
      address: address ?? this.address,
      distanceMeters: distanceMeters ?? this.distanceMeters,
    );
  }
}
