import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/features/profile/data/data_sources/profile_remote_data_sourc.dart';
import 'package:tukxi/features/profile/data/models/file_upload_response.dart';
import 'package:tukxi/features/profile/domain/repositories/profile_repository.dart';

class ProfileRepositoryImpl implements ProfileRepository {
  final ProfileRemoteDataSource remoteDataSource;

  ProfileRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, FileUploadResponse>> uploadProfileFile({
    required File file,
  }) async {
    return handleApiCall(
      () => remoteDataSource.uploadProfileFile(file: file),
      apiErrorMessage: 'Failed to upload profile file.',
    );
  }
}
