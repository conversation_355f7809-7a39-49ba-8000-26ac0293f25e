import 'package:tukxi/features/auth/data/models/auth_user.dart';
import 'package:tukxi/features/favourite_locations/data/models/location_response.dart';
import 'package:tukxi/features/favourite_locations/domain/entities/favourite_address_entity.dart';

class FavouriteLocation extends FavouriteLocationEntity {
  FavouriteLocation({
    super.id,
    super.userProfileId,
    super.name,
    super.description,
    super.location,
    super.meta,
    super.user,
    super.createdAt,
    super.updtaedat,
  });

  factory FavouriteLocation.fromJson(Map<String, dynamic> json) {
    return FavouriteLocation(
      id: json['id'] as String?,
      userProfileId: json['userProfileId'] as String?,
      name: json['name'] as String?,
      description: json['description'] as String?,
      location: json['location'] != null
          ? LocationResponse.fromJson(json['location'] as Map<String, dynamic>)
          : null,
      meta: json['meta'] != null
          ? LocationMetaResponse.fromJson(json['meta'] as Map<String, dynamic>)
          : null,
      user: json['user'] != null
          ? AuthUser.fromJson(json['user'] as Map<String, dynamic>)
          : null,
      createdAt: json['createdAt'] != null
          ? DateTime.tryParse(json['createdAt'] as String)
          : null,
      updtaedat: json['updtaedat'] != null
          ? DateTime.tryParse(json['updtaedat'] as String)
          : null,
    );
  }
}

class FavouriteLocationListResponse extends FavouriteLocationListEntity {
  FavouriteLocationListResponse({
    super.data,
    super.total,
    super.page,
    super.limit,
  });

  factory FavouriteLocationListResponse.fromJson(Map<String, dynamic> json) {
    return FavouriteLocationListResponse(
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => FavouriteLocation.fromJson(e as Map<String, dynamic>))
          .toList(),
      total: json['total'] as int?,
      page: json['page'] as int?,
      limit: json['limit'] as int?,
    );
  }
}
