import 'package:flutter/material.dart';
import 'package:tukxi/core/widgets/circular_image_widget.dart';
import 'package:tukxi/core/widgets/remote_image_widget.dart';

class DriverImageWithCar extends StatelessWidget {
  const DriverImageWithCar({
    super.key,
    required this.imageUrl,
    this.carImageUrl,
  });

  final String? carImageUrl;
  final String imageUrl;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(color: Colors.white, width: 119, height: 113),
        // Car image
        Positioned(
          top: 0,
          child: Container(
            width: 100,
            height: 75,
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(8)),
            child: RemoteImageWidget(
              imageUrl: carImageUrl,
              fit: BoxFit.cover,
              borderRadius: 0,
            ),
          ),
        ),

        // // Driver avatar
        Positioned(
          top: 40,
          right: 0,
          child: Container(
            width: 64,
            height: 64,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 1),
            ),
            child: CircularImageWidget(
              size: 64,
              borderRadius: 32,
              imageUrl: imageUrl,
            ),
          ),
        ),
      ],
    );
  }
}
