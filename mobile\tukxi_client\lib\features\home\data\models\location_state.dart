import 'package:google_maps_flutter/google_maps_flutter.dart';

class LocationState {
  LocationState({
    this.gpsLocation,
    this.ipLocation,
    this.countryCode,
    this.gpsEnabled = false,
    this.permissionGranted = false,
    this.showPermissionBanner = false,
  });

  final LatLng? gpsLocation;
  final LatLng? ipLocation;
  final String? countryCode;
  final bool gpsEnabled;
  final bool permissionGranted;
  final bool showPermissionBanner;

  LocationState copyWith({
    LatLng? gpsLocation,
    LatLng? ipLocation,
    String? countryCode,
    bool? gpsEnabled,
    bool? permissionGranted,
    bool? showPermissionBanner,
  }) {
    return LocationState(
      gpsLocation: gpsLocation ?? this.gpsLocation,
      ipLocation: ipLocation ?? this.ipLocation,
      gpsEnabled: gpsEnabled ?? this.gpsEnabled,
      permissionGranted: permissionGranted ?? this.permissionGranted,
      showPermissionBanner: showPermissionBanner ?? this.showPermissionBanner,
      countryCode: countryCode ?? this.countryCode,
    );
  }

  LatLng? get bestAvailable => gpsLocation ?? ipLocation;
}
