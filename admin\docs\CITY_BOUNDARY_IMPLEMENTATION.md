# City Boundary Drawing Implementation Plan - Tukxi Admin

## Overview
Implement polygon drawing functionality for city boundaries in the existing Tukxi admin panel. Users can draw city service areas by clicking points on Google Maps, with auto-closing polygon behavior similar to Figma's pen tool.

## Current Codebase Analysis

### ✅ Existing Infrastructure
- **Polygon Support**: `City` type already includes `polygon?: LatLng[]` field
- **API Ready**: `useUpdateCity` mutation can handle polygon updates  
- **UI Integration Point**: city-details-page.tsx has "City Boundaries" tab
- **Data Types**: `LatLng` interface matches requirements exactly
- **Map Library**: `@vis.gl/react-google-maps` already integrated

### Current Architecture
```
admin/src/modules/city/
├── api/
│   ├── queries.ts      # useGetCity, useListCities
│   └── mutations.ts    # useUpdateCity (will handle polygon updates)
├── components/         # Will add CityBoundaryDrawer here
├── pages/
│   └── city-details-page.tsx  # Integration point (City Boundaries tab)
└── types/
    └── city.ts        # LatLng interface already exists
```

## Implementation Strategy

### Phase 1: Core Drawing Integration

#### 1.1 Required Dependencies
**Library Migration Required**: `@vis.gl/react-google-maps` doesn't support Drawing Manager.
- **Remove**: `@vis.gl/react-google-maps` 
- **Install**: `@react-google-maps/api` (has Drawing Manager support)
- **Enable**: `drawing` and `places` libraries in Google Maps API

```bash
# Remove old library
pnpm remove @vis.gl/react-google-maps

# Install new library with drawing support
pnpm add @react-google-maps/api
```

#### 1.2 Component Structure
```
admin/src/modules/city/components/
├── boundary-drawer/
│   ├── CityBoundaryDrawer.tsx     # Main container
│   ├── DrawingControls.tsx        # Drawing UI controls  
│   ├── MapWithDrawing.tsx         # Map + Drawing Manager
│   └── CitySearch.tsx             # Places autocomplete
```

### Phase 2: Core Features Implementation

#### 2.1 Drawing Workflow
1. **Start Drawing**: Enable Drawing Manager in polygon mode
2. **Click Points**: User clicks map to add vertices (minimum 3 points)
3. **Auto-Close**: Double-click or complete drawing auto-closes polygon
4. **Save**: Extract coordinates and call `useUpdateCity` mutation

#### 2.2 Data Flow
```typescript
// Drawing → Coordinate Extraction → API Update
const handlePolygonComplete = (polygon: google.maps.Polygon) => {
  const coordinates = extractPolygonCoordinates(polygon);
  updateCityMutation.mutate({
    id: cityId,
    polygon: coordinates
  });
};
```

#### 2.3 API Integration
Use existing hooks - no new API endpoints needed:
```typescript
// Loading existing polygon
const { data: city } = useGetCity(cityId);

// Saving polygon changes  
const updateCityMutation = useUpdateCity();
```

### Phase 3: UX Features

#### 3.1 Drawing Modes
- **Drawing Mode**: Create new city boundary
- **Edit Mode**: Modify existing boundary (drag vertices, add/remove points)
- **View Mode**: Display existing boundaries (read-only)

#### 3.2 Visual Design
- Semi-transparent polygon fill (35% opacity)
- Clear stroke for boundary definition
- Visual feedback during drawing (line indicators)
- Drawing instructions and status

#### 3.3 City Search & Navigation
- Google Places Autocomplete for city search
- Auto-center map on selected city
- Appropriate zoom levels for boundary work

## Technical Implementation Details

### Drawing Manager Configuration
```typescript
import { useJsApiLoader, GoogleMap, DrawingManager } from '@react-google-maps/api';

const drawingManagerOptions = {
  drawingMode: google.maps.drawing.OverlayType.POLYGON,
  drawingControl: true,
  drawingControlOptions: {
    position: google.maps.ControlPosition.TOP_CENTER,
    drawingModes: [google.maps.drawing.OverlayType.POLYGON]
  },
  polygonOptions: {
    fillColor: '#FF6B6B',
    fillOpacity: 0.35,
    strokeColor: '#E55555',
    strokeOpacity: 0.8,
    strokeWeight: 2,
    editable: true,
    clickable: true
  }
};
```

### Coordinate Extraction
```typescript
const extractPolygonCoordinates = (polygon: google.maps.Polygon): LatLng[] => {
  const path = polygon.getPath();
  const coordinates: LatLng[] = [];
  
  for (let i = 0; i < path.getLength(); i++) {
    const point = path.getAt(i);
    coordinates.push({
      lat: point.lat(),
      lng: point.lng()
    });
  }
  
  // Ensure closed polygon (first point = last point)
  if (coordinates.length > 0) {
    coordinates.push(coordinates[0]);
  }
  
  return coordinates;
};
```

### Integration with Existing City Page
Update city-details-page.tsx to replace static map with interactive boundary drawer:

```typescript
// Replace existing map in City Boundaries tab
<TabsContent value='boundaries' className='mt-0'>
  <div className='h-[60vh] w-full rounded-lg overflow-hidden border'>
    <CityBoundaryDrawer 
      cityId={cityId}
      existingPolygon={cityData.polygon}
    />
  </div>
</TabsContent>
```

## Implementation Timeline

### Week 1: Core Drawing
- [x] Update implementation documentation
- [ ] Install and configure Drawing Manager
- [ ] Create basic CityBoundaryDrawer component
- [ ] Implement drawing controls UI
- [ ] Basic polygon drawing functionality

### Week 2: API Integration & Features  
- [ ] Connect to useUpdateCity mutation
- [ ] Load and display existing city polygons
- [ ] Add city search with Places Autocomplete
- [ ] Implement polygon editing mode
- [ ] Add validation and error handling

### Week 3: Polish & Integration
- [ ] Visual styling and UX improvements
- [ ] Integrate into city-details-page.tsx
- [ ] Testing and bug fixes
- [ ] Documentation updates

## Validation & Error Handling

### Client-Side Validation
- Minimum 3 points required for valid polygon
- Self-intersection prevention using Google Maps geometry library
- Maximum reasonable polygon size limits

### API Integration
- Use existing error handling from useUpdateCity mutation
- Optimistic updates with rollback on failure
- Loading states during save operations

## Expected Features

### Core Functionality
✅ **Auto-closing polygons** - Built into Drawing Manager
✅ **Figma-like pen tool experience** - Click to add points, double-click to finish  
✅ **Polygon editing** - Drag vertices, add/remove points
✅ **API integration** - Save to existing backend via useUpdateCity
✅ **City search** - Places Autocomplete for navigation

### User Experience
- Clear drawing instructions and visual feedback
- Drawing mode indicators and status
- Smooth transitions between drawing/editing/viewing modes
- Proper error messages and validation feedback

## Benefits of This Approach

### Leverage Existing Infrastructure
- No API changes required - uses existing `useUpdateCity` mutation
- Builds on current React Query setup and error handling
- Extends existing city-details-page.tsx without major refactoring
- Uses established component patterns and styling

### Minimal Code Complexity
- ~200-300 lines of new code (vs 500+ in original plan)
- 3-4 focused components instead of complex architecture
- Direct integration with existing city management workflow

### Production Ready
- Proper TypeScript integration with existing types
- Follows established patterns in the codebase
- Comprehensive error handling and validation
- Mobile-friendly responsive design

This implementation provides the exact "Figma pen tool" experience requested while seamlessly integrating with your existing, well-architected city management system.