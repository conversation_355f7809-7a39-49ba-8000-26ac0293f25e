model ChargeGroup {
  id                String              @id @default(uuid()) @map("id") @db.Uuid
  name              String              @map("name")
  description       String?             @map("description")
  identifier        String              @unique @map("identifier")
  createdAt         DateTime            @default(now()) @map("created_at")
  updatedAt         DateTime            @updatedAt @map("updated_at")
  deletedAt         DateTime?           @map("deleted_at") @db.Timestamptz
  ChargeGroupCharge ChargeGroupCharge[]
  fareChargeGroups  FareChargeGroup[]

  @@index([name], name: "idx_charge_group_name")
  @@index([identifier], name: "idx_charge_group_identifier")
  @@map("charge_groups")
}
