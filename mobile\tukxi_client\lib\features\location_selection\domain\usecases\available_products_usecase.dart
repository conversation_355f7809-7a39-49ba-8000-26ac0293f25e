import 'package:dartz/dartz.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/features/location_selection/data/models/available_option.dart';
import 'package:tukxi/features/location_selection/domain/repositories/booking_repository.dart';

class AvailableProductsUsecase {
  AvailableProductsUsecase({required this.repository});

  final BookingRepository repository;

  Future<Either<Failure, AvailableOption>> executefetchAvailableRideOptions({
    required LatLng pickupLocation,
    required LatLng destinationLocation,
    required RideTimeOption rideTimeOption,
    required DateTime pickupTime,
  }) {
    return repository.fetchAvailableRideOptions(
      pickupLocation: pickupLocation,
      destinationLocation: destinationLocation,
      rideTimeOption: rideTimeOption,
      pickupTime: pickupTime,
    );
  }
}
