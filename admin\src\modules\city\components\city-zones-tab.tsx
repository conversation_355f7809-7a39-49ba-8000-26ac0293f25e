'use client';

import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { useState } from 'react';
import { toast } from '@/lib/toast';
import { useCityZones } from '../api/city-zone-queries';
import { useGetCity } from '../api/queries';
import { Zone } from '../types/city-zone';
import { CityZoneFilters } from './city-zone-filters';
import { CityZoneTable } from './city-zone-table';
import { CityZoneModal } from './city-zone-modal';
import { DeleteCityZoneModal } from './delete-city-zone-modal';
import { CityZoneMapDrawer } from './city-zone-map-drawer';

interface CityZonesTabProps {
   cityId: string;
}

export function CityZonesTab({ cityId }: CityZonesTabProps) {
   const [search, setSearch] = useState('');
   const [zoneTypeId, setZoneTypeId] = useState<string | undefined>(undefined);

   // Modal states
   const [zoneModalOpen, setZoneModalOpen] = useState(false);
   const [deleteModalOpen, setDeleteModalOpen] = useState(false);
   const [mapDrawerOpen, setMapDrawerOpen] = useState(false);
   const [selectedZone, setSelectedZone] = useState<Zone | null>(null);

   // Reset filters
   const handleSearchChange = (value: string) => {
      setSearch(value);
   };

   const handleZoneTypeChange = (value: string | undefined) => {
      setZoneTypeId(value);
   };

   const cityZonesQuery = useCityZones(cityId, { includeRelations: true });
   const { data: currentCity } = useGetCity(cityId);

   // Apply filters client-side since the city zones API doesn't have built-in filtering
   const filteredZones =
      cityZonesQuery.data?.data?.filter(zone => {
         const matchesSearch =
            !search ||
            zone.name.toLowerCase().includes(search.toLowerCase()) ||
            zone.description?.toLowerCase().includes(search.toLowerCase());
         const matchesZoneType = !zoneTypeId || zone.zoneTypeId === zoneTypeId;
         return matchesSearch && matchesZoneType;
      }) || [];

   const totalZones = filteredZones.length;

   // Create data structure similar to city products
   const tableData = {
      data: filteredZones,
   };

   // Handle modal actions
   const handleEditClick = (zone: Zone) => {
      setSelectedZone(zone);
      setZoneModalOpen(true);
   };

   const handleAddClick = () => {
      // Check if city has a boundary polygon
      const cityPolygon = currentCity?.data?.polygon;
      if (!cityPolygon || cityPolygon.length < 1) {
         toast.error('Please draw the city boundary first before creating zones.');
         return;
      }

      setSelectedZone(null);
      setZoneModalOpen(true);
   };

   const handleDeleteClick = (zone: Zone) => {
      setSelectedZone(zone);
      setDeleteModalOpen(true);
   };

   const handleMapClick = (zone: Zone) => {
      setSelectedZone(zone);
      setMapDrawerOpen(true);
   };

   const handleZoneCreated = (zone: Zone) => {
      // Check if city has a boundary polygon before opening map
      const cityPolygon = currentCity?.data?.polygon;
      if (cityPolygon && cityPolygon.length >= 1) {
         setSelectedZone(zone);
         setMapDrawerOpen(true);
      }
      // If no city boundary, don't open map (user will get error when they try to manually open it)
   };

   // Check if any filters are active
   const hasFilters = !!search || !!zoneTypeId;

   return (
      <div className='space-y-4'>
         {/* Header with Add Button */}
         <div className='flex justify-between items-center'>
            <div>
               <h3 className='text-lg font-semibold text-gray-900'>City Zones</h3>
               <p className='text-sm text-gray-600'>Manage zones within this city</p>
            </div>
            <div className='flex items-center gap-4'>
               {/* Zone Info */}
               <div className='flex gap-2'>
                  <div className='flex items-center gap-2 px-3 py-2 bg-gray-50 rounded-md border'>
                     <span className='text-sm text-gray-600'>Total Zones</span>
                     <span className='inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-blue-700 bg-blue-100 rounded-full'>
                        {totalZones}
                     </span>
                  </div>
               </div>
               {/* Add Zone Button */}
               <Button variant={'outline'} className='gap-2' onClick={handleAddClick}>
                  <Plus className='h-4 w-4' />
                  Add Zone
               </Button>
            </div>
         </div>

         {/* Table Card */}
         <Card className='overflow-hidden py-4 px-4 rounded-sm'>
            <CityZoneFilters
               search={search}
               zoneTypeId={zoneTypeId}
               onSearchChange={handleSearchChange}
               onZoneTypeChange={handleZoneTypeChange}
               isLoading={cityZonesQuery.isFetching && !cityZonesQuery.isLoading}
            />

            <CityZoneTable
               data={tableData}
               isLoading={cityZonesQuery.isLoading}
               hasFilters={hasFilters}
               onEditClick={handleEditClick}
               onDeleteClick={handleDeleteClick}
               onMapClick={handleMapClick}
            />
         </Card>

         {/* Zone Modal (Create/Edit) */}
         <CityZoneModal
            cityId={cityId}
            zone={selectedZone}
            open={zoneModalOpen}
            onOpenChange={setZoneModalOpen}
            onZoneCreated={handleZoneCreated}
         />

         <DeleteCityZoneModal
            open={deleteModalOpen}
            onOpenChange={setDeleteModalOpen}
            zone={selectedZone}
         />

         {/* Map Drawer */}
         <CityZoneMapDrawer
            open={mapDrawerOpen}
            onOpenChange={setMapDrawerOpen}
            zone={selectedZone}
         />
      </div>
   );
}
