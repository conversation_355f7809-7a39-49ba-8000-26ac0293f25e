import 'package:tukxi/features/auth/data/models/auth_user.dart';
import 'package:tukxi/features/booking/domain/entities/ride_entity.dart';

class Driver extends DriverEntity {
  Driver({
    super.id,
    super.firstName,
    super.lastName,
    super.profilePic,
    super.user,
    super.role,
    super.verificationCode,
    super.driverMeta,
    super.vehicleClass,
    super.vehicleColor,
    super.vehicleModel,
    super.vehicleRegNumber,
    super.driverRating,
  });

  factory Driver.fromJson(Map<String, dynamic> json) => Driver(
    id: json['id'] as String?,
    firstName: json['firstName'] as String?,
    lastName: json['lastName'] as String?,
    verificationCode: json['verificationCode'] as int?,
    vehicleClass: json['vehicleClass'] as String?,
    vehicleColor: json['vehicleColor'] as String?,
    vehicleModel: json['vehicleModel'] as String?,
    vehicleRegNumber: json['vehicleRegNumber'] as String?,
    user: json['user'] == null ? null : AuthUser.fromJson(json['user']),
    role: json['role'] == null ? null : Role.fromJson(json['role']),
    driverMeta: json['driverMeta'] == null
        ? null
        : DriverMeta.fromJson(json['metaData']),
    driverRating: json['driverRating'] == null
        ? 0
        : (json['driverRating'] as num?)?.toDouble(),
  );

  Map<String, dynamic> toJson() => {
    'id': id,
    'firstName': firstName,
    'lastName': lastName,
    'verificationCode': verificationCode,
    'profilePic': profilePic,
    'vehicleClass': vehicleClass,
    'vehicleColor': vehicleColor,
    'vehicleModel': vehicleModel,
    'vehicleRegNumber': vehicleRegNumber,
    'user': user?.toJson(),
    'role': role?.toJson(),
    'driverMeta': driverMeta?.toJson(),
  };
}

class DriverMeta extends DriverMetaEntity {
  DriverMeta({super.avgRating});

  factory DriverMeta.fromJson(Map<String, dynamic> json) {
    return DriverMeta(
      avgRating: json['avgRating'] == null
          ? 0
          : (json['avgRating'] as num?)?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() => {'avgRating': avgRating};
}

class Role extends RoleEntity {
  Role({required super.id, required super.name});

  factory Role.fromJson(Map<String, dynamic> json) =>
      Role(id: json['id'], name: json['name']);

  Map<String, dynamic> toJson() => {'id': id, 'name': name};
}
