-- CreateTable
CREATE TABLE "charge_groups" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "identifier" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "charge_groups_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "charge_groups_identifier_key" ON "charge_groups"("identifier");

-- CreateIndex
CREATE INDEX "idx_charge_group_name" ON "charge_groups"("name");

-- CreateIndex
CREATE INDEX "idx_charge_group_identifier" ON "charge_groups"("identifier");
