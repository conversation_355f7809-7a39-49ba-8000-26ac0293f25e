import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/theme/app_colors.dart';

class BottomBookingOptions extends StatelessWidget {
  const BottomBookingOptions({
    super.key,
    required this.title,
    required this.onBookButtonTapped,
  });

  final String? title;
  final VoidCallback onBookButtonTapped;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 25),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Divider(thickness: 1, height: 1, color: AppColors.black15),
          Row(
            children: [
              Expanded(
                child: TextButton(
                  onPressed: () {},
                  style: TextButton.styleFrom(
                    splashFactory: NoSplash.splashFactory,
                    foregroundColor: Colors.black,
                    overlayColor: Colors.transparent,
                  ).merge(Theme.of(context).textButtonTheme.style),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Image.asset(AssetPaths.cash, height: 20, width: 20),
                      const SizedBox(width: 10),
                      Text(
                        'Cash/UPI',
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Expanded(
                child: TextButton(
                  onPressed: () {},
                  style: TextButton.styleFrom(
                    splashFactory: NoSplash.splashFactory,
                    foregroundColor: Colors.black,
                    overlayColor: Colors.transparent,
                  ).merge(Theme.of(context).textButtonTheme.style),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      SvgPicture.asset(AssetPaths.personal, height: 18),
                      const SizedBox(width: 10),
                      Text(
                        'Personal',
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 5),
          SizedBox(
            width: double.infinity,
            child: FilledButton(
              onPressed: onBookButtonTapped,
              style: FilledButton.styleFrom(
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ).merge(Theme.of(context).filledButtonTheme.style),
              child: Text(
                'Book${title != null ? ' $title' : ''}',
                style: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
