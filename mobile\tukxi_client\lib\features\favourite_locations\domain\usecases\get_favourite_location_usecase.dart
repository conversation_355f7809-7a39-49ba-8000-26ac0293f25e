// Get all favourite locations
import 'package:dartz/dartz.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/features/favourite_locations/data/models/favourite_location.dart';
import 'package:tukxi/features/favourite_locations/domain/repositories/favourite_location_repository.dart';

class GetFavouriteLocationsUseCase {
  final FavouriteRepository repository;
  GetFavouriteLocationsUseCase(this.repository);

  Future<Either<Failure, List<FavouriteLocation>>> execute() {
    return repository.getFavouriteLocations();
  }
}
