import 'package:dartz/dartz.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/features/location_selection/data/models/place_suggestion.dart';
import 'package:tukxi/features/home/<USER>/params/location_params.dart';

abstract class GooglePlacesRepository {
  Future<Either<Failure, List<PlaceSuggestion>>> getSuggestions({
    required String query,
    required LatLng? location,
  });
  Future<Either<Failure, LatLng?>> getPlaceCoordinates({
    required String placeId,
  });

  Future<Either<Failure, LocationParams>> getGooglePlaceDetails(
    LatLng? currentLocation,
  );

  Future<Either<Failure, LocationParams>> getAccuratePlace(
    LatLng? currentLocation,
  );
}
