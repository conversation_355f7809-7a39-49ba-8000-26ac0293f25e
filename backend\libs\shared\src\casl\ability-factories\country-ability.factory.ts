import {
  AbilityBuilder,
  ExtractSubjectType,
  MongoAbility,
  createMongoAbility,
} from '@casl/ability';
import { Injectable } from '@nestjs/common';
import { ApiConsumer } from '@shared/shared/modules/auth/interfaces';

type Action = 'create' | 'read' | 'update' | 'delete' | 'manage';

type Subjects = 'Country' | 'all';

export type CountryAbility = MongoAbility<[Action, Subjects]>;

@Injectable()
export class CountryAbilityFactory {
  constructor() {}

  async createForAPIConsumer(apiConsumer: ApiConsumer) {
    const { can, build } = new AbilityBuilder(createMongoAbility);

    // Get user permissions from token
    const userPermissions = apiConsumer.permissions || [];

    // Define permissions based on token permissions

    // country:create permission
    if (userPermissions.includes('country:create')) {
      can('create', 'Country');
    }

    // country:list permission
    if (userPermissions.includes('country:list')) {
      can('read', 'Country');
    }

    // country:edit permission
    if (userPermissions.includes('country:edit')) {
      can('update', 'Country');
    }

    // country:manage permission
    if (userPermissions.includes('country:manage')) {
      can('manage', 'Country');
    }

    // country:delete permission (if you want to separate it from manage)
    if (userPermissions.includes('country:delete')) {
      can('delete', 'Country');
    }

    return build({
      detectSubjectType: (item: any) =>
        item.constructor as ExtractSubjectType<Subjects>,
    });
  }
}
