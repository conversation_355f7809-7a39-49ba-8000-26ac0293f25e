import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/features/booking/data/models/driver.dart';
import 'package:tukxi/features/booking/presentation/screens/ratings_screen.dart';
import 'package:tukxi/main.dart';
import 'package:tukxi/routes/app_routes.dart';

class RatingDialogService {
  /// Show rating dialog and handle navigation
  static Future<void> showRatingDialog({
    required BuildContext context,
    required Driver? driverDetails,
    required String rideId,
    VoidCallback? onReviewSubmitted,
    String? redirectRoute,
    bool shouldNavigateToTabbar = true,
    bool isFromTripDetails = false,
  }) async {
    showDialog(
      barrierColor: AppColors.black40,
      context: context,
      barrierDismissible: false, // ✅ Prevent dismissing without rating
      builder: (ctx) => RatingsScreen(
        driverDetails: driverDetails,
        isFromTripDetails: isFromTripDetails,
        rideId: rideId,
        onClose: () {
          ctx.pop();
          if (!isFromTripDetails) {
            Future.delayed(const Duration(milliseconds: 100), () {
              if (!context.mounted) return;
              _handleNavigation(
                context: context,
                redirectRoute: redirectRoute,
                shouldNavigateToTabbar: shouldNavigateToTabbar,
              );
            });
          }
        },
        onReviewSubmitted: () {
          ctx.pop();
          onReviewSubmitted?.call();
        },
      ),
    );
  }

  /// ✅ Handle navigation after rating
  static void _handleNavigation({
    required BuildContext context,
    String? redirectRoute,
    bool shouldNavigateToTabbar = true,
  }) {
    final originalContext = navigatorKey.currentContext;

    if (context.mounted && originalContext != null && originalContext.mounted) {
      if (redirectRoute != null) {
        // ✅ Navigate to custom route
        originalContext.go(redirectRoute);
      } else if (shouldNavigateToTabbar) {
        // ✅ Default navigation to tabbar
        originalContext.go(AppRoutes.tabbar);
      }
      // ✅ If shouldNavigateToTabbar is false, stay on current screen
    }
  }

  // /// ✅ Show rating with completion callback
  // static Future<void> showRatingWithCallback({
  //   required BuildContext context,
  //   required Driver? driverDetails,
  //   required String rideId,
  //   required VoidCallback onComplete,
  // }) async {
  //   return showRatingDialog(
  //     context: context,
  //     driverDetails: driverDetails,
  //     rideId: rideId,
  //     onRatingComplete: onComplete,
  //     shouldNavigateToTabbar: false,
  //   );
  // }

  // /// ✅ Show rating and redirect to specific route
  // static Future<void> showRatingAndRedirect({
  //   required BuildContext context,
  //   required Driver? driverDetails,
  //   required String rideId,
  //   required String redirectRoute,
  // }) async {
  //   return showRatingDialog(
  //     context: context,
  //     driverDetails: driverDetails,
  //     rideId: rideId,
  //     redirectRoute: redirectRoute,
  //     shouldNavigateToTabbar: false,
  //   );
  // }
}
