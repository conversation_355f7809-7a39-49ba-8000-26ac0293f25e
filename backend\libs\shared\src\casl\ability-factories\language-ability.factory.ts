import {
  AbilityBuilder,
  ExtractSubjectType,
  MongoAbility,
  createMongoAbility,
} from '@casl/ability';
import { Injectable } from '@nestjs/common';
import { ApiConsumer } from '@shared/shared/modules/auth/interfaces';

type Action = 'create' | 'list' | 'edit' | 'delete';

type Subjects = 'Language' | 'all';

export type LanguageAbility = MongoAbility<[Action, Subjects]>;

@Injectable()
export class LanguageAbilityFactory {
  constructor() {}

  async createForAPIConsumer(apiConsumer: ApiConsumer) {
    const { can, build } = new AbilityBuilder(createMongoAbility);

    // Get user permissions from token
    const userPermissions = apiConsumer.permissions || [];

    // Define permissions based on token permissions

    // language:create permission
    if (userPermissions.includes('language:create')) {
      can('create', 'Language');
    }

    // language:list permission
    if (userPermissions.includes('language:list')) {
      can('list', 'Language');
    }

    // language:edit permission
    if (userPermissions.includes('language:edit')) {
      can('edit', 'Language');
    }

    // language:delete permission (if you want to separate it from manage)
    if (userPermissions.includes('language:delete')) {
      can('delete', 'Language');
    }

    return build({
      detectSubjectType: (item: any) =>
        item.constructor as ExtractSubjectType<Subjects>,
    });
  }
}
