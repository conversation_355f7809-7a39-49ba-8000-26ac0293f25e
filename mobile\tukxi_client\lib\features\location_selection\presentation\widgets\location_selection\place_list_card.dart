import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/features/location_selection/data/models/place_suggestion.dart';

class PlaceListCard extends StatelessWidget {
  const PlaceListCard({
    super.key,
    required this.location,
    required this.imageWidth,
  });

  final PlaceSuggestion location;
  final double imageWidth;
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      children: [
        // if (location.$1)
        //   SvgPicture.asset(AssetPaths.recent, width: width)
        // else
        SizedBox(
          width: imageWidth,
          child: SvgPicture.asset(AssetPaths.locationPin, width: 20),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                location.name,
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                location.address,
                style: GoogleFonts.inter(
                  fontSize: 10,
                  fontWeight: FontWeight.w400,
                  color: AppColors.black50,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
