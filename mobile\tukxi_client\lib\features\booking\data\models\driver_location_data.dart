class DriverLocationData {
  const DriverLocationData({required this.locationData, this.timeStamp});

  final LocationData? locationData;
  final DateTime? timeStamp;

  factory DriverLocationData.fromJson(Map<String, dynamic> json) {
    return DriverLocationData(
      locationData: json['locationData'] == null
          ? null
          : LocationData.fromJson(json['locationData']),
      timeStamp: json['timeStamp'] == null
          ? null
          : DateTime.parse(json['timestamp']),
    );
  }
}

class LocationData {
  final String? driverId;
  final double? lat;
  final double? lon;
  final double? bearing;
  final double? speed;

  final String? status; // can be enum later: online, offline, busy
  final String? rideId;
  final DateTime? timestamp;

  LocationData({
    required this.driverId,
    required this.lat,
    required this.lon,
    required this.bearing,
    required this.speed,
    required this.status,
    required this.rideId,
    required this.timestamp,
  });

  factory LocationData.fromJson(Map<String, dynamic> json) {
    return LocationData(
      driverId: json['driverId'] as String? ?? '',
      lat: (json['lat'] as num?)?.toDouble() ?? 0,
      lon: (json['lon'] as num?)?.toDouble() ?? 0,
      bearing: (json['bearing'] as num?)?.toDouble() ?? 0,
      speed: (json['speed'] as num?)?.toDouble() ?? 0,
      status: json['status'] as String? ?? '',
      rideId: json['rideId'] as String? ?? '',
      timestamp: json['timestamp'] != null
          ? null
          : DateTime.parse(json['timestamp']),
    );
  }
}
