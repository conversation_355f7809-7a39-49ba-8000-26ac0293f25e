import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/features/home/<USER>/models/location_state.dart';

//rideRefreshProvider use to refresh the home screen when ride  cancelled. It triggers the _fetchAllActiveRides method in home screen.
final rideRefreshProvider = StateProvider<bool>((_) => false);

final locationProvider = StateNotifierProvider<LocationNotifier, LocationState>(
  (ref) => LocationNotifier(),
);

class LocationNotifier extends StateNotifier<LocationState> {
  LocationNotifier() : super(LocationState());

  void updateCountryCode(String? countryCode) {
    if (state.countryCode == countryCode) return;
    state = state.copyWith(countryCode: countryCode);
  }

  void updateGpsLocationStatus({
    required bool gpsEnabled,
    required bool permissionGranted,
    LatLng? gpsLocation,
  }) {
    if (state.gpsLocation == gpsLocation &&
        state.gpsEnabled == gpsEnabled &&
        state.permissionGranted == permissionGranted) {
      return;
    }
    state = state.copyWith(
      gpsLocation: gpsLocation,
      gpsEnabled: gpsEnabled,
      permissionGranted: permissionGranted,
    );
  }

  void updateIpLocation(LatLng? loc) {
    if (state.ipLocation == loc) return;
    state = state.copyWith(ipLocation: loc);
  }

  void updateGpsEnabled(bool enabled) {
    if (state.gpsEnabled == enabled) return;
    state = state.copyWith(gpsEnabled: enabled);
  }

  void updatePermissionGranted(bool granted) {
    if (state.permissionGranted == granted) return;
    state = state.copyWith(permissionGranted: granted);
  }
}
