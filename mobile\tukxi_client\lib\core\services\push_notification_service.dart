import 'dart:io';

import 'package:engagespot_sdk/engagespot_sdk.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:tukxi/core/config/env_config.dart';
import 'package:tukxi/core/services/shared_preference_service.dart';

class PushNotificationService {
  bool _initialized = false;
  final _prefService = SharedPreferenceService();

  // final FlutterLocalNotificationsPlugin _localNotifications =
  //     FlutterLocalNotificationsPlugin();

  // Callbacks for different notification events
  Function(String title, String body, Map<String, dynamic> data)?
  onForegroundMessage;
  Function(Map<String, dynamic> data)? onNotificationTapped;
  void Function()? _onAllRead;
  bool _engagespotLoggedIn = false;

  /// Initialize Firebase + Engagespot SDK
  Future<void> init() async {
    if (_initialized) return;

    try {
      debugPrint("🔄 Initializing PushNotificationService...");
      await _requestNotificationPermission();
      // await _initLocalNotifications();
      // Init Engagespot
      Engagespot.initSdk(apiKey: EnvConfig.engagespotApiKey, isDebug: true);

      _setupTokenChangeListner();
      _setupFirebaseNotificationListeners();

      _initialized = true;
      debugPrint("✅ PushNotificationService initialized");
    } catch (e) {
      debugPrint("❌ PushNotificationService init failed: $e");
    }
  }

  /// Authenticate user with Engagespot + register FCM token
  Future<void> loginUser({required String userId}) async {
    try {
      await Engagespot.LoginUser(userId: userId);
      _engagespotLoggedIn = true;
      debugPrint("✅ User logged in to Engagespot: $userId");
      await _registerFcmToken();
      _setupEngagespotListeners();
    } catch (e) {
      debugPrint("❌ Failed to login user to Engagespot: $e");
      _engagespotLoggedIn = false;
    }
  }

  /// Register FCM token with Engagespot
  Future<void> _registerFcmToken() async {
    final token = await FirebaseMessaging.instance.getToken();
    if (token != null) {
      _prefService.fcmToken = token;
      try {
        await Engagespot.RegisterFCM(token);
        debugPrint("📲 Registered FCM token with Engagespot: $token");
      } catch (e) {
        debugPrint("❌ Failed to register FCM token with Engagespot: $e");

        Future.delayed(Duration(seconds: 3), () async {
          try {
            await Engagespot.RegisterFCM(token);
            debugPrint("✅ FCM token registered on retry");
          } catch (retryError) {
            debugPrint("❌ Retry failed: $retryError");
          }
        });
      }
    }
  }

  void _setupTokenChangeListner() {
    FirebaseMessaging.instance.onTokenRefresh.listen((newToken) async {
      if (_prefService.fcmToken != newToken) {
        _prefService.fcmToken = newToken;

        try {
          await Engagespot.RegisterFCM(newToken);
          debugPrint("🔄 Updated FCM token with Engagespot: $newToken");
        } catch (e) {
          debugPrint("❌ Failed to update FCM token with Engagespot: $e");
        }
      }
    });
  }

  /// Setup all notification listeners
  void _setupFirebaseNotificationListeners() {
    // Foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      final title = message.notification?.title ?? "";
      final body = message.notification?.body ?? "";
      final Map<String, dynamic> payload = message.data;
      debugPrint("🔔 Foreground push: $title - $body");
      debugPrint("📦 Payload: $payload");

      // If Engagespot format
      if (payload.containsKey("triggerData")) {
        final triggerData = payload["triggerData"];
        debugPrint("🚗 Ride ID: ${triggerData['rideId']}");
        debugPrint("📊 Status: ${triggerData['status']}");
      }
      // Show local notification
      // _showLocalNotification(title, body, message.data);

      // Call callback
      onForegroundMessage?.call(title, body, message.data);
    });

    // Notification tapped - app in background
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      debugPrint('👆 Notification tapped: ${message.data}');
      _handleNotificationTap(message.data);
    });

    // Notification tapped - app terminated
    FirebaseMessaging.instance.getInitialMessage().then((message) {
      if (message != null) {
        debugPrint("🚀 Notification tapped (terminated): ${message.data}");
        _handleNotificationTap(message.data);
      }
    });
  }

  /// ✅ Setup Engagespot listeners (separate method, called after login)
  void _setupEngagespotListeners() {
    if (!_engagespotLoggedIn) {
      debugPrint("❌ Cannot setup Engagespot listeners - user not logged in");
      return;
    }

    try {
      debugPrint("🔄 Setting up Engagespot listeners...");

      // ✅ Fixed typo: ListenMessage (not ListernMessage)
      Engagespot.ListernMessage(
        onReadAll: () {
          debugPrint("📖 Engagespot: All messages read");
          _onAllRead?.call();
        },
        onMessage: (message) {
          debugPrint("💌 🔥 ENGAGESPOT MESSAGE RECEIVED!");
          debugPrint("💌 Title: ${message.title}");
          debugPrint("💌 Message: ${message.message}");
          debugPrint("💌 Data: ${message.data}");

          // Handle th

          _handleEngagespotMessage(message);
          if (message.data != null) {
            _handleRideNotification(message.data);
          }
        },
      );

      debugPrint("✅ Engagespot listeners setup complete");
    } catch (e) {
      debugPrint("❌ Failed to setup Engagespot listeners: $e");
    }
  }

  void _handleEngagespotMessage(dynamic message) {
    final title = message.title ?? '';
    final body = message.body ?? '';
    final data = message.data as Map<String, dynamic>? ?? {};

    debugPrint('📱 Engagespot in-app message: $title');

    // Call callback
    onForegroundMessage?.call(title, body, {
      ...data,
      'source': 'engagespot',
      'messageId': message.id,
    });
  }

  /// ✅ Handle ride-specific notifications
  void _handleRideNotification(dynamic data) {
    try {
      if (data != null && data is Map<String, dynamic>) {
        final triggerData = data['triggerData'] as Map<String, dynamic>?;
        if (triggerData != null) {
          final rideId = triggerData['rideId'] as String?;
          final status = triggerData['status'] as String?;

          debugPrint("🚗 Ride notification - ID: $rideId, Status: $status");

          // Handle different statuses
          switch (status?.toLowerCase()) {
            case 'trip_completed':
              debugPrint("🏁 Trip completed for ride: $rideId");
              // Clear active ride
              _prefService.clearActiveRideId();
              break;
            case 'trip_started':
              debugPrint("🚀 Trip started for ride: $rideId");
              break;
            case 'accepted':
              debugPrint("✅ Ride accepted: $rideId");
              break;
            case 'cancelled':
              debugPrint("❌ Ride cancelled: $rideId");
              _prefService.clearActiveRideId();
              break;
          }
        }
      }
    } catch (e) {
      debugPrint("❌ Error handling ride notification: $e");
    }
  }

  // /// Handle local notification tap
  // void _handleLocalNotificationTap(String? payload) {
  //   if (payload != null) {
  //     // Parse payload and handle
  //     debugPrint('👆 Local notification tapped: $payload');
  //   }
  // }

  /// Handle notification tap with routing logic
  void _handleNotificationTap(Map<String, dynamic> data) {
    onNotificationTapped?.call(data);
  }

  Future<void> _requestNotificationPermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.notification.request();
      debugPrint("🔔 Android notification permission: $status");
    } else if (Platform.isIOS) {
      // ✅ Don't request iOS permissions here - already done in main
      debugPrint("🍏 iOS permissions already requested in main");
    }
  }

  // /// Initialize local notifications
  // Future<void> _initLocalNotifications() async {
  //   const androidSettings = AndroidInitializationSettings(
  //     '@mipmap/ic_launcher',
  //   );
  //   const iosSettings = DarwinInitializationSettings(
  //     requestAlertPermission: true,
  //     requestBadgePermission: true,
  //     requestSoundPermission: true,
  //   );

  //   const settings = InitializationSettings(
  //     android: androidSettings,
  //     iOS: iosSettings,
  //   );

  //   await _localNotifications.initialize(
  //     settings,
  //     onDidReceiveNotificationResponse: (response) {
  //       _handleLocalNotificationTap(response.payload);
  //     },
  //   );
  // }

  // /// Show local notification
  // Future<void> _showLocalNotification(
  //   String title,
  //   String body,
  //   Map<String, dynamic> data,
  // ) async {
  //   const androidDetails = AndroidNotificationDetails(
  //     'engagespot_channel',
  //     'Engagespot Notifications',
  //     channelDescription: 'Notifications from Engagespot',
  //     importance: Importance.high,
  //     priority: Priority.high,
  //   );

  //   const iosDetails = DarwinNotificationDetails(
  //     presentAlert: true,
  //     presentBadge: true,
  //     presentSound: true,
  //   );

  //   const details = NotificationDetails(
  //     android: androidDetails,
  //     iOS: iosDetails,
  //   );

  //   await _localNotifications.show(
  //     DateTime.now().millisecondsSinceEpoch ~/ 1000,
  //     title,
  //     body,
  //     details,
  //     payload: data.toString(),
  //   );
  // }

  /// Set callback for when all notifications are marked as read
  void onAllRead(void Function() callback) {
    _onAllRead = callback;
  }

  /// Check if initialized
  bool get isInitialized => _initialized;
}
