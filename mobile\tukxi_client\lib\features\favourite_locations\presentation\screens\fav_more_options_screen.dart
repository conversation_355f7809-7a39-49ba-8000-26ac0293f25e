import 'dart:io';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/widgets/cancel_button.dart';
import 'package:tukxi/core/widgets/drag_handle.dart';

class FavMoreOptionsScreen extends StatelessWidget {
  final String addressType;
  final String address;
  final VoidCallback onEdit;
  final VoidCallback onDelete;
  const FavMoreOptionsScreen({
    super.key,
    required this.addressType,
    required this.address,
    required this.onEdit,
    required this.onDelete,
  });
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: Platform.isAndroid,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Drag handle
            DragHandle(),

            const SizedBox(height: 30),
            RichText(
              text: TextSpan(
                children: [
                  TextSpan(
                    text: '$addressType | ',
                    style: GoogleFonts.inter(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                      color: AppColors.black60,
                    ),
                  ),
                  TextSpan(
                    text: address,
                    style: GoogleFonts.inter(
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                      color: AppColors.black50,
                    ),
                  ),
                ],
              ),
            ),
            const Divider(
              height: 50,
              thickness: 0.75,
              color: AppColors.black10,
            ),

            SizedBox(
              width: double.infinity,
              height: 45,
              child: FilledButton.icon(
                onPressed: onEdit,
                icon: const Icon(Icons.edit_outlined),
                label: Text(
                  'Edit',
                  style: GoogleFonts.inter(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
            CancelButton(onCancelPressed: onDelete, title: 'Delete'),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
