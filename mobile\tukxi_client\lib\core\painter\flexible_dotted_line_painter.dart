import 'package:flutter/material.dart';

class FlexibleDottedLinePainter extends CustomPainter {
  final Color color;
  final double dotRadius;
  final double spacing;

  FlexibleDottedLinePainter({
    required this.color,
    this.dotRadius = 1.0,
    this.spacing = 3.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final double centerX = size.width / 2;
    final double dotDiameter = dotRadius * 2;
    final double totalStep = dotDiameter + spacing;

    // Calculate number of dots that fit in the available height
    final int dotCount = (size.height / totalStep).floor();

    // Calculate actual spacing to distribute dots evenly
    final double actualSpacing = dotCount > 1
        ? size.height / (dotCount - 1)
        : 0;

    for (int i = 0; i < dotCount; i++) {
      final double y = i * actualSpacing;

      canvas.drawCircle(Offset(centerX, y + dotRadius), dotRadius, paint);
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
