import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { RideStatus } from '@shared/shared/modules/ride-matching/constants';

export class AdminLocationDto {
  @ApiProperty({ example: 12.9716 })
  lat!: number;

  @ApiProperty({ example: 77.5946 })
  lng!: number;

  @ApiPropertyOptional({ example: 'Koramangala, Bangalore' })
  address?: string;
}

export class AdminUserDto {
  @ApiProperty({ example: 'user-uuid-123' })
  id!: string;

  @ApiPropertyOptional({ example: '+************' })
  phoneNumber?: string | null;

  @ApiPropertyOptional({ example: '<EMAIL>' })
  email?: string | null;
}

export class AdminUserProfileDto {
  @ApiProperty({ example: 'profile-uuid-123' })
  id!: string;

  @ApiProperty({ example: 'John' })
  firstName!: string;

  @ApiProperty({ example: 'Doe' })
  lastName!: string;

  @ApiPropertyOptional({
    example: 'https://s3.amazonaws.com/bucket/profile.jpg',
    nullable: true,
  })
  profilePictureUrl?: string | null;

  @ApiPropertyOptional({ example: 4.5 })
  averageRating?: number;

  @ApiProperty({ type: AdminUserDto })
  user!: AdminUserDto;
}

export class AdminVehicleTypeDto {
  @ApiProperty({ example: 'vehicle-type-uuid-123' })
  id!: string;

  @ApiProperty({ example: 'Auto Rickshaw' })
  name!: string;
}

export class AdminDriverVehicleDto {
  @ApiProperty({ example: 'vehicle-uuid-123' })
  id!: string;

  @ApiProperty({ example: 'KA01AB1234' })
  vehicleNumber!: string;

  @ApiProperty({ type: AdminVehicleTypeDto })
  vehicleType!: AdminVehicleTypeDto;
}

export class AdminProductDetailsDto {
  @ApiProperty({ example: 'product-uuid-123' })
  id!: string;

  @ApiProperty({ example: 'TukTuk Standard' })
  name!: string;

  @ApiPropertyOptional({ example: 'Standard auto rickshaw service' })
  description?: string | null;

  @ApiPropertyOptional({
    example: 'https://s3.amazonaws.com/bucket/icon.png',
    nullable: true,
  })
  icon?: string | null;
}

export class AdminReviewByDto {
  @ApiProperty({ example: 'reviewer-uuid-123' })
  id!: string;

  @ApiProperty({ example: 'John' })
  firstName!: string;

  @ApiProperty({ example: 'Doe' })
  lastName!: string;
}

export class AdminReviewDto {
  @ApiProperty({ example: 'review-uuid-123' })
  id!: string;

  @ApiProperty({ example: 4.5 })
  rating!: number;

  @ApiPropertyOptional({ example: 'Great ride, very comfortable!' })
  review?: string | null;

  @ApiProperty({ example: '2024-01-15T11:30:00.000Z' })
  createdAt!: Date;

  @ApiProperty({ type: AdminReviewByDto })
  reviewBy!: AdminReviewByDto;
}

export class AdminRideLifecycleDto {
  @ApiProperty({ example: 'lifecycle-uuid-123' })
  id!: string;

  @ApiProperty({
    enum: RideStatus,
    example: RideStatus.TRIP_COMPLETED,
  })
  status!: RideStatus;

  @ApiProperty({ example: '2024-01-15T10:30:00.000Z' })
  createdAt!: Date;

  @ApiPropertyOptional({
    description: 'Additional metadata for the lifecycle event',
    nullable: true,
  })
  meta?: any;
}

export class AdminRideDetailsDto {
  @ApiProperty({ example: 'ride-uuid-123' })
  id!: string;

  @ApiProperty({
    enum: RideStatus,
    example: RideStatus.TRIP_COMPLETED,
  })
  status!: RideStatus;

  @ApiProperty({ type: AdminLocationDto })
  pickupLocation!: AdminLocationDto;

  @ApiProperty({ type: AdminLocationDto })
  destinationLocation!: AdminLocationDto;

  @ApiPropertyOptional({
    type: [AdminLocationDto],
    description: 'Stops along the route',
  })
  stops?: AdminLocationDto[] | null;

  @ApiPropertyOptional({ example: '1234' })
  verificationCode?: string | null;

  @ApiProperty({ example: '2024-01-15T10:30:00.000Z' })
  createdAt!: Date;

  @ApiPropertyOptional({ example: '2024-01-15T11:15:00.000Z' })
  completedAt?: Date | null;

  @ApiPropertyOptional({ example: '2024-01-15T10:45:00.000Z' })
  otpVerifiedAt?: Date | null;

  @ApiPropertyOptional({ example: 2700, description: 'Duration in seconds' })
  duration?: number | null;

  @ApiPropertyOptional({ example: 5.2, description: 'Distance in kilometers' })
  distance?: number | null;

  @ApiProperty({ type: AdminUserProfileDto })
  rider!: AdminUserProfileDto;

  @ApiPropertyOptional({ type: AdminUserProfileDto })
  driver?: AdminUserProfileDto | null;

  @ApiProperty({ type: AdminProductDetailsDto })
  product!: AdminProductDetailsDto;

  @ApiPropertyOptional({ type: AdminDriverVehicleDto })
  driverVehicle?: AdminDriverVehicleDto | null;

  @ApiProperty({ type: [AdminRideLifecycleDto] })
  rideLifecycles!: AdminRideLifecycleDto[];

  @ApiProperty({ type: [AdminReviewDto] })
  reviews!: AdminReviewDto[];
}

export class AdminRideDetailsResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Ride details retrieved successfully' })
  message!: string;

  @ApiProperty({ type: AdminRideDetailsDto })
  data!: AdminRideDetailsDto;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}
