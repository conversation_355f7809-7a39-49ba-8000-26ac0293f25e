import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/location_constants.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/enums/enum_mappers.dart';
import 'package:tukxi/core/formatters/input_formatters.dart';
import 'package:tukxi/core/models/address_type.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/core/utils/snack_bar_utils.dart';
import 'package:tukxi/core/widgets/custom_textfield.dart';
import 'package:tukxi/core/widgets/loading_button.dart';
import 'package:tukxi/core/widgets/nav_back_button.dart';
import 'package:tukxi/features/favourite_locations/data/models/favourite_location.dart';
import 'package:tukxi/features/favourite_locations/presentation/provider/favourite_provider.dart';
import 'package:tukxi/features/favourite_locations/presentation/states/favourite_location_state.dart';
import 'package:tukxi/features/home/<USER>/params/location_params.dart';

class AddAddressConfirmSheet extends ConsumerStatefulWidget {
  const AddAddressConfirmSheet({
    super.key,
    required this.placeDetails,
    required this.scaffoldContext,
    required this.favouriteLocation,
  });

  final LocationParams? placeDetails;
  final FavouriteLocation? favouriteLocation;
  final BuildContext scaffoldContext;

  @override
  ConsumerState<AddAddressConfirmSheet> createState() {
    return _AddAddressConfirmSheetState();
  }
}

class _AddAddressConfirmSheetState
    extends ConsumerState<AddAddressConfirmSheet> {
  TextEditingController _nameController = TextEditingController();
  AddressType? selectedType;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.favouriteLocation != null) {
        final type = widget.favouriteLocation?.meta?.type;

        setState(() {
          _nameController.text = widget.favouriteLocation?.name ?? '';
          selectedType = AddressType(
            title: type ?? 'other',
            assetPath: 'assetPath',
          );
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(favouriteLocationProvider);
    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: Colors.transparent,
      body: Align(
        alignment: Alignment.bottomCenter,
        child: SingleChildScrollView(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(LocationConstants.mapCornerRadius),
                    topRight: Radius.circular(
                      LocationConstants.mapCornerRadius,
                    ),
                  ),
                ),
                child: SafeArea(
                  top: true,
                  bottom: Platform.isAndroid,
                  child: Stack(
                    children: [
                      SingleChildScrollView(
                        padding: EdgeInsets.only(
                          left: 15,
                          right: 15,
                          bottom: MediaQuery.of(context).viewInsets.bottom,
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            const SizedBox(height: 60),
                            Form(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(height: 20),
                                  Text(
                                    widget.placeDetails?.locationName ?? '',
                                    style: GoogleFonts.inter(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 20,
                                    ),
                                  ),

                                  const SizedBox(height: 4),

                                  Text(
                                    maxLines: 2,
                                    widget.placeDetails?.locationAddress ?? '',
                                    style: GoogleFonts.inter(
                                      color: AppColors.black50,
                                      fontWeight: FontWeight.w400,
                                      fontSize: 14,
                                    ),
                                  ),

                                  const SizedBox(height: 10),

                                  CustomFormTextField(
                                    controller: _nameController,
                                    hintText: 'Name',
                                    labelText: widget.favouriteLocation != null
                                        ? widget.favouriteLocation?.name ?? ''
                                        : '',
                                    inputFormatters: [
                                      InputFormatters.capitalizeFirstLetter(),
                                    ],
                                    errorBorder: (_nameController.text.isEmpty)
                                        ? const UnderlineInputBorder(
                                            borderSide: BorderSide(
                                              color: AppColors.red,
                                              width: 1.2,
                                            ),
                                          )
                                        : null,
                                  ),

                                  const SizedBox(height: 25),

                                  Text(
                                    'Save as',
                                    style: GoogleFonts.inter(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14,
                                    ),
                                  ),

                                  const SizedBox(height: 15),

                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: addressTypes.map((addressType) {
                                      return _buildAddressOptions(addressType);
                                    }).toList(),
                                  ),
                                ],
                              ),
                            ),

                            const SizedBox(height: 50),
                            LoadingButton(
                              isLoading: state is FavouriteLocationLoading,
                              onPressed: _saveAddress,
                              text: widget.favouriteLocation != null
                                  ? 'Update Address'
                                  : 'Save Address',
                            ),
                            const SizedBox(height: 25),
                          ],
                        ),
                      ),

                      Positioned(
                        top: 0,
                        left: 0,
                        right: 0,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            vertical: 10,
                            horizontal: 15,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(
                                LocationConstants.mapCornerRadius,
                              ),
                              topRight: Radius.circular(
                                LocationConstants.mapCornerRadius,
                              ),
                            ),
                          ),
                          alignment: Alignment.topLeft,
                          child: NavBackButton(onPressed: () => context.pop()),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddressOptions(AddressType addressType) {
    return Row(
      children: [
        InkWell(
          onTap: () {
            setState(() {
              selectedType = addressType;
            });
          },
          child: Container(
            height: 40,
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
            decoration: BoxDecoration(
              color: selectedType == addressType
                  ? AppColors.primary
                  : Colors.transparent,
              border: Border.all(width: 1, color: AppColors.black10),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              children: [
                Image.asset(
                  addressType.assetPath,
                  width: 12,
                  color: selectedType == addressType
                      ? Colors.white
                      : AppColors.black60,
                ),
                const SizedBox(width: 8),
                Text(
                  addressType.title,
                  style: GoogleFonts.inter(
                    color: selectedType == addressType
                        ? Colors.white
                        : AppColors.black50,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 15),
      ],
    );
  }

  void _saveAddress() {
    String errorMessage = '';

    if (_nameController.text.isEmpty) {
      errorMessage = 'Please enter a name for saving a favourite location';
    } else if (selectedType == null) {
      errorMessage = 'Select any type Home, work or others';
    }

    if (errorMessage.trim().isNotEmpty) {
      SnackbarUtils.showSnackBar(
        context: context,
        message: errorMessage,
        type: SnackBarType.error,
      );
    } else {
      if (widget.favouriteLocation == null) {
        _addFavouriteAddress();
      } else {
        _editFavouriteAddress();
      }
    }
  }

  Future _addFavouriteAddress() async {
    if (widget.placeDetails == null || widget.placeDetails!.latLng == null) {
      return;
    }

    final result = await ref
        .read(favouriteLocationProvider.notifier)
        .addFavourite(
          name: _nameController.text,
          latitude: widget.placeDetails!.latLng!.latitude,
          longitude: widget.placeDetails!.latLng!.longitude,
          description: widget.placeDetails?.locationName ?? '',
          meta: {
            'address': widget.placeDetails!.locationAddress ?? '',
            'type': selectedType?.title,
          },
        );

    result.fold(
      (failure) {
        if (!mounted) return;

        handleApiError(
          context: context,
          failure: failure,
          onRetry: () async => _addFavouriteAddress(),
        );
      },
      (response) {
        context.pop({'reload': true, 'update': false});
        context.pop({'reload': true, 'update': false});
      },
    );
  }

  Future _editFavouriteAddress() async {
    if (widget.placeDetails == null ||
        widget.placeDetails!.latLng == null ||
        widget.favouriteLocation == null ||
        widget.favouriteLocation!.id == null) {
      return;
    }

    final result = await ref
        .read(favouriteLocationProvider.notifier)
        .editFavourite(
          id: widget.favouriteLocation!.id!,
          name: _nameController.text,
          latitude: widget.placeDetails!.latLng!.latitude,
          longitude: widget.placeDetails!.latLng!.longitude,
          description: widget.placeDetails?.locationName ?? '',
          meta: {
            'address': widget.placeDetails!.locationAddress ?? '',
            'type': selectedType?.title,
          },
        );

    result.fold(
      (failure) {
        if (!mounted) return;

        handleApiError(
          context: context,
          failure: failure,
          onRetry: () async => _addFavouriteAddress(),
        );
      },
      (response) {
        context.pop({'reload': true, 'update': true});
        context.pop({'reload': true, 'update': true});
      },
    );
  }
}
