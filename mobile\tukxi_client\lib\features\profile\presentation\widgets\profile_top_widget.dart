import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/extensions/date_extensions.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/widgets/circular_image_widget.dart';
import 'package:tukxi/features/auth/data/models/auth_user.dart';

class ProfileTopWidget extends StatelessWidget {
  const ProfileTopWidget({super.key, required this.user});

  final AuthUser user;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        CircularImageWidget(
          size: 64,
          borderRadius: 32,
          imageUrl: user.profilePictureUrl,
        ),
        const SizedBox(width: 20),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                fullName(user.firstName ?? '', user.lastName ?? ''),
                style: GoogleFonts.inter(
                  fontSize: 24,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                'Rider Since ${user.createdAt?.toDateString()}',
                style: GoogleFonts.inter(
                  fontSize: 10,
                  color: AppColors.black50,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String fullName(String firstName, String lastName) {
    if (firstName.isEmpty && lastName.isEmpty) {
      return '';
    } else if (firstName.isEmpty) {
      return lastName.trim();
    } else if (lastName.isEmpty) {
      return firstName.trim();
    } else {
      return '$firstName $lastName'.trim();
    }
  }
}
