import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/theme/app_colors.dart';

class CancelRideButton extends StatelessWidget {
  const CancelRideButton({super.key, required this.cancelTapped});

  final VoidCallback cancelTapped;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 65,
      padding: const EdgeInsets.only(bottom: 15),
      child: FilledButton(
        onPressed: cancelTapped,
        style: FilledButton.styleFrom(
          backgroundColor: Colors.white,
          foregroundColor: AppColors.red,
          side: const BorderSide(color: AppColors.red, width: 1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8), // rounded corners
          ),
        ).merge(Theme.of(context).filledButtonTheme.style),
        child: Text(
          'Cancel Ride',
          style: GoogleFonts.inter(fontSize: 16, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }
}
