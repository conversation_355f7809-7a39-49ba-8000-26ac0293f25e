import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/core/utils/snack_bar_utils.dart';
import 'package:tukxi/core/widgets/platform_dialog.dart';
import 'package:tukxi/core/widgets/shimmer_widgets.dart';
import 'package:tukxi/core/widgets/title_bar.dart';
import 'package:go_router/go_router.dart';
import 'package:tukxi/features/favourite_locations/data/models/favourite_location.dart';
import 'package:tukxi/features/favourite_locations/presentation/provider/favourite_provider.dart';
import 'package:tukxi/features/favourite_locations/presentation/screens/fav_more_options_screen.dart';
import 'package:tukxi/features/favourite_locations/presentation/states/favourite_location_state.dart';
import 'package:tukxi/features/favourite_locations/presentation/widgets/favourite_location_card.dart';
import 'package:tukxi/routes/app_routes.dart';

class FavouriteLocationListingScreen extends ConsumerStatefulWidget {
  const FavouriteLocationListingScreen({super.key});

  @override
  ConsumerState<FavouriteLocationListingScreen> createState() {
    return _FavouriteLocationListingScreenState();
  }
}

class _FavouriteLocationListingScreenState
    extends ConsumerState<FavouriteLocationListingScreen> {
  List<FavouriteLocation> _savedAddresses = [];

  @override
  void initState() {
    super.initState();

    Future.microtask(() async {
      await _fetchAllFavouriteAddress();
    });
  }

  @override
  Widget build(BuildContext context) {
    final favState = ref.watch(favouriteLocationProvider);

    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: TitleBarWithBackButton(
        title: 'Saved Address',
        titleStyle: GoogleFonts.inter(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          height: 28 / 20,
        ),
        onBackPressed: () => context.pop(),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: AppColors.primary,
        shape: const CircleBorder(),
        onPressed: () async {
          final result = await context.push<Map<String, dynamic>>(
            AppRoutes.addAddress,
          );
          if (result != null) {
            final reload = result['reload'] as bool;
            final update = result['update'] as bool;
            if (reload) {
              if (!context.mounted) return;

              SnackbarUtils.showSnackBar(
                context: context,
                message: update
                    ? 'Address updated successfully.'
                    : 'Address saved successfully.',
                type: SnackBarType.success,
              );
              _fetchAllFavouriteAddress();
            }
          }
        },
        child: const Icon(Icons.add, color: Colors.white, size: 28),
      ),
      body: SafeArea(
        bottom: Platform.isAndroid,
        child: Stack(
          children: [
            Column(children: [const SizedBox(height: 15), _buildFavList()]),
            if (favState is FavouriteLocationLoading && _savedAddresses.isEmpty)
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    ShimmerLoaders.rideOptionShimmer(),
                    ShimmerLoaders.rideOptionShimmer(),
                    ShimmerLoaders.rideOptionShimmer(),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFavList() {
    final favState = ref.watch(favouriteLocationProvider);

    return Expanded(
      child: (favState is! FavouriteLocationLoading && _savedAddresses.isEmpty)
          ? Center(
              child: Text(
                'No favorites yet!',
                style: GoogleFonts.inter(
                  fontSize: 14,
                  color: AppColors.black50,
                ),
              ),
            )
          : ListView.separated(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _savedAddresses.length,
              separatorBuilder: (_, _) => const SizedBox(height: 25),
              itemBuilder: (context, index) {
                final address = _savedAddresses[index];
                return InkWell(
                  onTap: () {
                    context.pop({'address': address});
                  },
                  child: FavouriteLocationCard(
                    address: address,
                    onOptionsTapped: onMoreOptionTapped,
                  ),
                );
              },
            ),
    );
  }

  Future _fetchAllFavouriteAddress() async {
    final result = await ref
        .read(favouriteLocationProvider.notifier)
        .fetchFavourites();

    result.fold(
      (failure) {
        if (!mounted) return;

        handleApiError(
          context: context,
          failure: failure,
          onRetry: () async => _fetchAllFavouriteAddress(),
        );
      },
      (response) {
        setState(() {
          _savedAddresses = response;
        });
      },
    );
  }

  Future _deleteFavouriteAddress(FavouriteLocation address) async {
    if (address.id == null) return;
    final result = await ref
        .read(favouriteLocationProvider.notifier)
        .deleteFavourite(id: address.id!);

    result.fold(
      (failure) {
        if (!mounted) return;

        handleApiError(
          context: context,
          failure: failure,
          onRetry: () async => _fetchAllFavouriteAddress(),
        );
      },
      (response) {
        bool isSuccess = response.success ?? false;
        if (isSuccess) {
          _fetchAllFavouriteAddress();
          SnackbarUtils.showSnackBar(
            context: context,
            message: 'Address deleted successfully.',
            type: SnackBarType.success,
          );
        } else {
          SnackbarUtils.showSnackBar(
            context: context,
            message: 'Unable to delete favourite',
            type: SnackBarType.success,
          );
        }
      },
    );
  }

  void onMoreOptionTapped(FavouriteLocation address) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (ctx) => FavMoreOptionsScreen(
        addressType: address.meta?.type ?? '',
        address: address.meta?.address ?? '',
        onEdit: () async {
          ctx.pop();

          final result = await context.push<Map<String, dynamic>>(
            AppRoutes.addAddress,
            extra: {'favouriteLocation': address},
          );
          if (result != null) {
            final reload = result['reload'] as bool;
            if (reload) {
              if (!mounted) return;
              SnackbarUtils.showSnackBar(
                context: context,
                message: 'Address updated successfully.',
                type: SnackBarType.success,
              );
              _fetchAllFavouriteAddress();
            }
          }
        },
        onDelete: () {
          ctx.pop();
          PlatformDialog.showConfirmationDialog(
            context: context,
            title: 'Delete',
            content: 'Are you sure you want to delete this favorite?',
            confirmText: 'Delete',
            onConfirm: () {
              _deleteFavouriteAddress(address);
            },
          );
        },
      ),
    );
  }
}
