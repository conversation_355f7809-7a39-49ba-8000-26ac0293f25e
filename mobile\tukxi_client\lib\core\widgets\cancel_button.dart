import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/theme/app_colors.dart';

class CancelButton extends StatelessWidget {
  const CancelButton({
    super.key,
    required this.onCancelPressed,
    required this.title,
    this.height,
  });

  final String title;
  final double? height;
  final VoidCallback onCancelPressed;
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: height ?? 45,
      child: TextButton.icon(
        onPressed: onCancelPressed,
        icon: title.toLowerCase() == 'cancel'
            ? null
            : const Icon(Icons.delete_outline, color: AppColors.red),
        style: TextButton.styleFrom(
          side: const BorderSide(color: AppColors.red, width: 1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8), // rounded corners
          ),
        ),
        label: Text(
          title,
          style: GoogleFonts.inter(
            fontWeight: FontWeight.w500,
            color: AppColors.red,
            fontSize: 16,
          ),
        ),
      ),
    );
  }
}
