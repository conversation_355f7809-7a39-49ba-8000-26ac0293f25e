'use client';

import { useState, useEffect } from 'react';
import {
   <PERSON><PERSON>,
   DialogContent,
   DialogHeader,
   DialogTitle,
   DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { Spinner } from '@/components/ui/spinner';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { useAttachCharge } from '../api/charge-mutations';
import { useListAllCharges } from '../api/charge-queries';

interface AttachChargeModalProps {
   chargeGroupId: string;
   isOpen: boolean;
   onClose: () => void;
}

export function AttachChargeModal({ chargeGroupId, isOpen, onClose }: AttachChargeModalProps) {
   const [selectedChargeId, setSelectedChargeId] = useState<string>('');
   const [priority, setPriority] = useState<string>('0');

   const queryClient = useQueryClient();
   const attachChargeMutation = useAttachCharge();
   const { data: allChargesData, isLoading: isLoadingCharges } = useListAllCharges();

   // Reset form when modal opens
   useEffect(() => {
      if (isOpen) {
         setSelectedChargeId('');
         setPriority('0');
      }
   }, [isOpen]);

   const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();

      if (!selectedChargeId) {
         toast.error('Please select a charge');
         return;
      }

      const priorityNum = parseInt(priority, 10);
      if (isNaN(priorityNum)) {
         toast.error('Priority must be a valid number');
         return;
      }

      attachChargeMutation.mutate(
         {
            chargeGroupId,
            request: {
               chargeId: selectedChargeId,
               priority: priorityNum,
            },
         },
         {
            onSuccess: () => {
               toast.success('Charge attached successfully');
               queryClient.invalidateQueries({ queryKey: ['charges', chargeGroupId] });
               onClose();
            },
         }
      );
   };

   return (
      <Dialog open={isOpen} onOpenChange={onClose}>
         <DialogContent className='sm:max-w-[500px]'>
            <DialogHeader>
               <DialogTitle>Add Existing Charge</DialogTitle>
            </DialogHeader>

            <form onSubmit={handleSubmit}>
               <div className='space-y-4 py-4'>
                  {/* Charge Selection */}
                  <div className='space-y-2'>
                     <Label htmlFor='charge'>Select Charge</Label>
                     {isLoadingCharges ? (
                        <div className='flex items-center justify-center py-4'>
                           <Spinner className='h-6 w-6' />
                        </div>
                     ) : (
                        <Select value={selectedChargeId} onValueChange={setSelectedChargeId}>
                           <SelectTrigger className='w-full' id='charge'>
                              <SelectValue placeholder='Select a charge to add' />
                           </SelectTrigger>
                           <SelectContent>
                              {allChargesData?.data?.map(charge => (
                                 <SelectItem key={charge.id} value={charge.id}>
                                    {charge.name}{' '}
                                    {charge.identifier ? `(${charge.identifier})` : ''}
                                 </SelectItem>
                              ))}
                           </SelectContent>
                        </Select>
                     )}
                  </div>

                  {/* Priority Input */}
                  <div className='space-y-2'>
                     <Label htmlFor='priority'>Priority</Label>
                     <Input
                        id='priority'
                        type='number'
                        value={priority}
                        onChange={e => setPriority(e.target.value)}
                        placeholder='Enter priority (e.g., 0, 1, 2...)'
                     />
                     <p className='text-xs text-gray-500'>
                        Lower priority values are executed first
                     </p>
                  </div>
               </div>

               <DialogFooter>
                  <Button
                     type='button'
                     variant='outline'
                     onClick={onClose}
                     disabled={attachChargeMutation.isPending}
                  >
                     Cancel
                  </Button>
                  <Button
                     type='submit'
                     disabled={attachChargeMutation.isPending || !selectedChargeId}
                  >
                     {attachChargeMutation.isPending ? (
                        <>
                           <Spinner className='h-4 w-4 mr-2' />
                           Adding...
                        </>
                     ) : (
                        'Add Charge'
                     )}
                  </Button>
               </DialogFooter>
            </form>
         </DialogContent>
      </Dialog>
   );
}
