'use client';

import {
   Sheet,
   <PERSON><PERSON><PERSON>ontent,
   She<PERSON><PERSON>eader,
   SheetTitle,
} from '@/components/ui/sheet';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
   Timeline,
   TimelineContent,
   TimelineDate,
   TimelineHeader,
   TimelineIndicator,
   TimelineItem,
   TimelineSeparator,
   TimelineTitle,
} from '@/components/ui/timeline';
import { useGetRideDetails } from '../api/queries';
import { RideDetails } from '../types/ride';
import { format } from 'date-fns';

interface RideDetailsSheetProps {
   rideId: string | null;
   isOpen: boolean;
   onClose: () => void;
}

function formatStatus(status: string): string {
   return status
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
}

function getStatusBadgeClass(status: string): string {
   const normalizedStatus = status.toLowerCase();

   if (normalizedStatus.includes('completed')) {
      return 'bg-green-50 text-green-700 border-green-200';
   }
   if (normalizedStatus.includes('cancelled')) {
      return 'bg-red-50 text-red-700 border-red-200';
   }
   if (normalizedStatus.includes('progress') || normalizedStatus.includes('accepted')) {
      return 'bg-blue-50 text-blue-700 border-blue-200';
   }
   if (normalizedStatus.includes('requested') || normalizedStatus.includes('processing')) {
      return 'bg-yellow-50 text-yellow-700 border-yellow-200';
   }
   return 'bg-gray-50 text-gray-700 border-gray-200';
}

function formatDuration(seconds: number | null | undefined): string {
   if (!seconds) return 'N/A';
   const hours = Math.floor(seconds / 3600);
   const minutes = Math.floor((seconds % 3600) / 60);
   if (hours > 0) {
      return `${hours}h ${minutes}m`;
   }
   return `${minutes}m`;
}

function formatDistance(km: number | null | undefined): string {
   if (!km) return 'N/A';
   return `${km.toFixed(2)} km`;
}

export function RideDetailsSheet({ rideId, isOpen, onClose }: RideDetailsSheetProps) {
   const { data, isLoading } = useGetRideDetails(rideId);
   const rideDetails: RideDetails | undefined = data?.data;

   return (
      <Sheet open={isOpen} onOpenChange={onClose}>
         <SheetContent className='sm:max-w-xl'>
            <SheetHeader className='pb-4'>
               <SheetTitle>Ride Details</SheetTitle>
            </SheetHeader>

            <ScrollArea className='h-[calc(100vh-6rem)] px-6'>
               {isLoading && (
                  <div className='space-y-4'>
                     <div className='h-6 bg-gray-200 rounded animate-pulse w-32' />
                     <div className='h-4 bg-gray-200 rounded animate-pulse w-48' />
                     <div className='h-4 bg-gray-200 rounded animate-pulse w-full' />
                     <div className='h-4 bg-gray-200 rounded animate-pulse w-full' />
                  </div>
               )}

               {!isLoading && rideDetails && (
                  <div className='space-y-6'>
                     {/* Combined Ride Info & Metrics */}
                     <div className='bg-gray-50 rounded-lg p-4 border border-gray-200'>
                        <div className='space-y-3 text-sm'>
                           <div>
                              <div className='text-gray-500 text-xs mb-1'>Ride ID</div>
                              <div className='font-mono text-xs text-gray-900 break-all bg-white px-2 py-1.5 rounded border border-gray-200'>
                                 {rideDetails.id}
                              </div>
                           </div>

                           <div className='grid grid-cols-3 gap-3'>
                              <div>
                                 <div className='text-gray-500 text-xs mb-1'>Status</div>
                                 <span className={`inline-flex items-center px-2 py-0.5 rounded-md text-xs font-medium border ${getStatusBadgeClass(rideDetails.status)}`}>
                                    {formatStatus(rideDetails.status)}
                                 </span>
                              </div>
                              <div>
                                 <div className='text-gray-500 text-xs mb-1'>Duration</div>
                                 <div className='text-gray-900 font-semibold'>
                                    {formatDuration(rideDetails.duration)}
                                 </div>
                              </div>
                              <div>
                                 <div className='text-gray-500 text-xs mb-1'>Distance</div>
                                 <div className='text-gray-900 font-semibold'>
                                    {formatDistance(rideDetails.distance)}
                                 </div>
                              </div>
                           </div>

                           <div className='grid grid-cols-2 gap-3'>
                              <div>
                                 <div className='text-gray-500 text-xs mb-1'>Created</div>
                                 <div className='text-gray-900 font-medium text-xs'>
                                    {format(new Date(rideDetails.createdAt), 'MMM dd, yyyy • hh:mm a')}
                                 </div>
                              </div>
                              {rideDetails.completedAt && (
                                 <div>
                                    <div className='text-gray-500 text-xs mb-1'>Completed</div>
                                    <div className='text-gray-900 font-medium text-xs'>
                                       {format(new Date(rideDetails.completedAt), 'MMM dd, yyyy • hh:mm a')}
                                    </div>
                                 </div>
                              )}
                           </div>

                           {rideDetails.otpVerifiedAt && (
                              <div>
                                 <div className='text-gray-500 text-xs mb-1'>OTP Verified</div>
                                 <div className='text-gray-900 font-medium text-xs'>
                                    {format(new Date(rideDetails.otpVerifiedAt), 'MMM dd, yyyy • hh:mm a')}
                                 </div>
                              </div>
                           )}
                           {rideDetails.verificationCode && (
                              <div>
                                 <div className='text-gray-500 text-xs mb-1'>Verification Code</div>
                                 <div className='text-gray-900 font-bold text-base tracking-wider'>
                                    {rideDetails.verificationCode}
                                 </div>
                              </div>
                           )}
                        </div>
                     </div>

                     {/* Rider Information */}
                     <div className='bg-blue-50 rounded-lg p-4 border border-blue-200'>
                        <h3 className='text-base font-semibold text-gray-900 mb-4'>
                           Rider Information
                        </h3>
                        <div className='space-y-3 text-sm'>
                           <div>
                              <div className='text-gray-500 text-xs mb-1'>Name</div>
                              <div className='text-gray-900 font-semibold'>
                                 {rideDetails.rider.firstName} {rideDetails.rider.lastName}
                              </div>
                           </div>
                           {rideDetails.rider?.user?.phoneNumber && (
                              <div>
                                 <div className='text-gray-500 text-xs mb-1'>Phone</div>
                                 <div className='text-gray-900 font-medium'>
                                    {rideDetails.rider.user.phoneNumber}
                                 </div>
                              </div>
                           )}
                           {rideDetails.rider?.user?.email && (
                              <div>
                                 <div className='text-gray-500 text-xs mb-1'>Email</div>
                                 <div className='text-gray-900 font-medium'>
                                    {rideDetails.rider.user.email}
                                 </div>
                              </div>
                           )}
                           {rideDetails.rider.averageRating && (
                              <div>
                                 <div className='text-gray-500 text-xs mb-1'>Rating</div>
                                 <div className='text-gray-900 font-semibold text-lg'>
                                    ⭐ {rideDetails.rider.averageRating.toFixed(1)} / 5.0
                                 </div>
                              </div>
                           )}
                        </div>
                     </div>

                     {/* Driver Information */}
                     {rideDetails.driver && (
                        <div className='bg-green-50 rounded-lg p-4 border border-green-200'>
                           <h3 className='text-base font-semibold text-gray-900 mb-4'>
                              Driver Information
                           </h3>
                           <div className='space-y-3 text-sm'>
                              <div>
                                 <div className='text-gray-500 text-xs mb-1'>Name</div>
                                 <div className='text-gray-900 font-semibold'>
                                    {rideDetails.driver.firstName} {rideDetails.driver.lastName}
                                 </div>
                              </div>
                              {rideDetails.driver?.user?.phoneNumber && (
                                 <div>
                                    <div className='text-gray-500 text-xs mb-1'>Phone</div>
                                    <div className='text-gray-900 font-medium'>
                                       {rideDetails.driver.user.phoneNumber}
                                    </div>
                                 </div>
                              )}
                              {rideDetails.driver?.user?.email && (
                                 <div>
                                    <div className='text-gray-500 text-xs mb-1'>Email</div>
                                    <div className='text-gray-900 font-medium'>
                                       {rideDetails.driver.user.email}
                                    </div>
                                 </div>
                              )}
                              {rideDetails.driver.averageRating && (
                                 <div>
                                    <div className='text-gray-500 text-xs mb-1'>Rating</div>
                                    <div className='text-gray-900 font-semibold text-lg'>
                                       ⭐ {rideDetails.driver.averageRating.toFixed(1)} / 5.0
                                    </div>
                                 </div>
                              )}
                           </div>
                        </div>
                     )}

                     {/* Product & Vehicle Information */}
                     <div className='bg-purple-50 rounded-lg p-4 border border-purple-200'>
                        <div className='grid grid-cols-2 gap-4 text-sm'>
                           <div>
                              <div className='text-gray-500 text-xs mb-1'>Product</div>
                              <div className='text-gray-900 font-semibold'>
                                 {rideDetails.product.name}
                              </div>
                           </div>
                           {rideDetails.driverVehicle && (
                              <>
                                 <div>
                                    <div className='text-gray-500 text-xs mb-1'>Vehicle Number</div>
                                    <div className='text-gray-900 font-bold tracking-wider'>
                                       {rideDetails.driverVehicle.vehicleNumber}
                                    </div>
                                 </div>
                                 <div className='col-span-2'>
                                    <div className='text-gray-500 text-xs mb-1'>Vehicle Type</div>
                                    <div className='text-gray-900 font-medium'>
                                       {rideDetails.driverVehicle.vehicleType.name}
                                    </div>
                                 </div>
                              </>
                           )}
                        </div>
                     </div>

                     {/* Key Timestamps */}
                     {rideDetails.rideLifecycles && rideDetails.rideLifecycles.length > 0 && (
                        <div className='bg-orange-50 rounded-lg p-4 border border-orange-200'>
                           <h3 className='text-base font-semibold text-gray-900 mb-4'>
                              Key Timestamps
                           </h3>
                           <div className='space-y-3 text-sm'>
                              {(() => {
                                 const requestedLifecycle = rideDetails.rideLifecycles.find(
                                    lc => lc.status.toLowerCase().includes('requested')
                                 );
                                 const acceptedLifecycle = rideDetails.rideLifecycles.find(
                                    lc => lc.status.toLowerCase().includes('accepted')
                                 );
                                 const startedLifecycle = rideDetails.rideLifecycles.find(
                                    lc => lc.status.toLowerCase().includes('started') ||
                                          lc.status.toLowerCase().includes('in_progress')
                                 );

                                 return (
                                    <>
                                       {requestedLifecycle && (
                                          <div>
                                             <div className='text-gray-500 text-xs mb-1'>
                                                Ride Request Time
                                             </div>
                                             <div className='text-gray-900 font-medium'>
                                                {format(
                                                   new Date(requestedLifecycle.createdAt),
                                                   'MMM dd, yyyy • hh:mm:ss a'
                                                )}
                                             </div>
                                          </div>
                                       )}
                                       {acceptedLifecycle && (
                                          <div>
                                             <div className='text-gray-500 text-xs mb-1'>
                                                Driver Accept Time
                                             </div>
                                             <div className='text-gray-900 font-medium'>
                                                {format(
                                                   new Date(acceptedLifecycle.createdAt),
                                                   'MMM dd, yyyy • hh:mm:ss a'
                                                )}
                                             </div>
                                          </div>
                                       )}
                                       {startedLifecycle && (
                                          <div>
                                             <div className='text-gray-500 text-xs mb-1'>
                                                Ride Start Time
                                             </div>
                                             <div className='text-gray-900 font-medium'>
                                                {format(
                                                   new Date(startedLifecycle.createdAt),
                                                   'MMM dd, yyyy • hh:mm:ss a'
                                                )}
                                             </div>
                                          </div>
                                       )}
                                    </>
                                 );
                              })()}
                           </div>
                        </div>
                     )}

                     {/* Locations */}
                     <div className='bg-gray-50 rounded-lg p-4 border border-gray-200'>
                        <h3 className='text-base font-semibold text-gray-900 mb-4'>Locations</h3>
                        <div className='space-y-4 text-sm'>
                           {(() => {
                              // Find timestamps from lifecycle metadata
                              const arrivedAtPickupLifecycle = rideDetails.rideLifecycles?.find(
                                 lc => lc.status.toLowerCase().includes('arrived') &&
                                       lc.meta?.locationType?.toLowerCase() === 'pickup'
                              );
                              const arrivedAtDestinationLifecycle = rideDetails.rideLifecycles?.find(
                                 lc => lc.status.toLowerCase().includes('completed') ||
                                       (lc.status.toLowerCase().includes('arrived') &&
                                        lc.meta?.locationType?.toLowerCase() === 'destination')
                              );

                              return (
                                 <>
                                    <div className='bg-white rounded-md p-3 border border-gray-200'>
                                       <div className='flex items-center gap-2 mb-2'>
                                          <div className='w-2 h-2 rounded-full bg-green-500'></div>
                                          <div className='text-gray-500 text-xs font-semibold'>Pickup</div>
                                       </div>
                                       <div className='font-medium text-gray-900 mb-1'>
                                          {rideDetails.pickupLocation.address || 'N/A'}
                                       </div>
                                       <div className='text-xs text-gray-400 font-mono mb-2'>
                                          {rideDetails.pickupLocation.lat.toFixed(6)},{' '}
                                          {rideDetails.pickupLocation.lng.toFixed(6)}
                                       </div>
                                       {arrivedAtPickupLifecycle && (
                                          <div className='mt-2 pt-2 border-t border-gray-200'>
                                             <div className='text-xs text-gray-500 mb-0.5'>Arrived At</div>
                                             <div className='text-xs text-gray-900 font-medium'>
                                                {format(
                                                   new Date(arrivedAtPickupLifecycle.createdAt),
                                                   'MMM dd, yyyy • hh:mm:ss a'
                                                )}
                                             </div>
                                          </div>
                                       )}
                                    </div>
                                    <div className='bg-white rounded-md p-3 border border-gray-200'>
                                       <div className='flex items-center gap-2 mb-2'>
                                          <div className='w-2 h-2 rounded-full bg-red-500'></div>
                                          <div className='text-gray-500 text-xs font-semibold'>Destination</div>
                                       </div>
                                       <div className='font-medium text-gray-900 mb-1'>
                                          {rideDetails.destinationLocation.address || 'N/A'}
                                       </div>
                                       <div className='text-xs text-gray-400 font-mono mb-2'>
                                          {rideDetails.destinationLocation.lat.toFixed(6)},{' '}
                                          {rideDetails.destinationLocation.lng.toFixed(6)}
                                       </div>
                                       {arrivedAtDestinationLifecycle && (
                                          <div className='mt-2 pt-2 border-t border-gray-200'>
                                             <div className='text-xs text-gray-500 mb-0.5'>Arrived At</div>
                                             <div className='text-xs text-gray-900 font-medium'>
                                                {format(
                                                   new Date(arrivedAtDestinationLifecycle.createdAt),
                                                   'MMM dd, yyyy • hh:mm:ss a'
                                                )}
                                             </div>
                                          </div>
                                       )}
                                    </div>
                                 </>
                              );
                           })()}
                           {rideDetails.stops && rideDetails.stops.length > 0 && (
                              <>
                                 {rideDetails.stops.map((stop, index) => {
                                    // Try to find when driver arrived at this stop from lifecycle metadata
                                    const arrivedAtStopLifecycle = rideDetails.rideLifecycles?.find(
                                       lc => lc.status.toLowerCase().includes('arrived') &&
                                             lc.meta?.locationType?.toLowerCase() === 'stop' &&
                                             lc.meta?.stopIndex === index
                                    );

                                    return (
                                       <div key={index} className='bg-white rounded-md p-3 border border-gray-200'>
                                          <div className='flex items-center gap-2 mb-2'>
                                             <div className='w-2 h-2 rounded-full bg-blue-500'></div>
                                             <div className='text-gray-500 text-xs font-semibold'>
                                                Stop {index + 1}
                                             </div>
                                          </div>
                                          <div className='font-medium text-gray-900 mb-1'>
                                             {stop.address || 'N/A'}
                                          </div>
                                          <div className='text-xs text-gray-400 font-mono mb-2'>
                                             {stop.lat.toFixed(6)}, {stop.lng.toFixed(6)}
                                          </div>
                                          {arrivedAtStopLifecycle && (
                                             <div className='mt-2 pt-2 border-t border-gray-200'>
                                                <div className='text-xs text-gray-500 mb-0.5'>Arrived At</div>
                                                <div className='text-xs text-gray-900 font-medium'>
                                                   {format(
                                                      new Date(arrivedAtStopLifecycle.createdAt),
                                                      'MMM dd, yyyy • hh:mm:ss a'
                                                   )}
                                                </div>
                                             </div>
                                          )}
                                       </div>
                                    );
                                 })}
                              </>
                           )}
                        </div>
                     </div>

                     {/* Ride Lifecycle Timeline */}
                     {rideDetails.rideLifecycles && rideDetails.rideLifecycles.length > 0 && (
                        <div className='bg-gray-50 rounded-lg p-4 border border-gray-200'>
                           <h3 className='text-base font-semibold text-gray-900 mb-6'>
                              Ride Lifecycle
                           </h3>
                           <Timeline
                              defaultValue={rideDetails.rideLifecycles.length}
                           >
                              {rideDetails.rideLifecycles.map((lifecycle, index) => (
                                 <TimelineItem key={lifecycle.id} step={index + 1}>
                                    <TimelineHeader>
                                       <TimelineSeparator />
                                       <TimelineDate className='text-gray-600'>
                                          {format(
                                             new Date(lifecycle.createdAt),
                                             'MMM dd, yyyy'
                                          )}
                                          {' • '}
                                          {format(
                                             new Date(lifecycle.createdAt),
                                             'hh:mm a'
                                          )}
                                       </TimelineDate>
                                       <TimelineTitle className='text-gray-900 font-semibold'>
                                          {formatStatus(lifecycle.status)}
                                       </TimelineTitle>
                                       <TimelineIndicator className='bg-white' />
                                    </TimelineHeader>
                                    {lifecycle.meta && Object.keys(lifecycle.meta).length > 0 && (
                                       <TimelineContent>
                                          <div className='text-xs text-gray-600 bg-white p-3 rounded border border-gray-200 mt-2'>
                                             <pre className='whitespace-pre-wrap break-words'>
                                                {(() => {
                                                   const formatMetaValue = (obj: any): any => {
                                                      if (obj === null || obj === undefined) return obj;

                                                      if (typeof obj === 'number' && obj > 1000000000000 && obj < 9999999999999) {
                                                         // Unix timestamp in milliseconds
                                                         return format(new Date(obj), 'MMM dd, yyyy • hh:mm:ss a');
                                                      }

                                                      if (typeof obj === 'string') {
                                                         // ISO 8601 date string
                                                         const isoDateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/;
                                                         if (isoDateRegex.test(obj)) {
                                                            return format(new Date(obj), 'MMM dd, yyyy • hh:mm:ss a');
                                                         }
                                                         return obj;
                                                      }

                                                      if (Array.isArray(obj)) {
                                                         return obj.map(formatMetaValue);
                                                      }

                                                      if (typeof obj === 'object') {
                                                         const formatted: any = {};
                                                         for (const key in obj) {
                                                            formatted[key] = formatMetaValue(obj[key]);
                                                         }
                                                         return formatted;
                                                      }

                                                      return obj;
                                                   };

                                                   return JSON.stringify(formatMetaValue(lifecycle.meta), null, 2);
                                                })()}
                                             </pre>
                                          </div>
                                       </TimelineContent>
                                    )}
                                 </TimelineItem>
                              ))}
                           </Timeline>
                        </div>
                     )}

                     {/* Reviews */}
                     {rideDetails.reviews && rideDetails.reviews.length > 0 && (
                        <div className='bg-gray-50 rounded-lg p-4 border border-gray-200'>
                           <h3 className='text-base font-semibold text-gray-900 mb-4'>Reviews</h3>
                           <div className='space-y-3'>
                              {rideDetails.reviews.map(review => (
                                 <div
                                    key={review.id}
                                    className='border rounded-lg p-4 bg-white border-gray-200'
                                 >
                                    <div className='flex justify-between items-start mb-3'>
                                       <div className='font-semibold text-sm text-gray-900'>
                                          {review.reviewBy.firstName} {review.reviewBy.lastName}
                                       </div>
                                       <div className='text-sm font-bold text-gray-900'>
                                          ⭐ {review.rating.toFixed(1)}
                                       </div>
                                    </div>
                                    {review.review && (
                                       <div className='text-sm text-gray-700 mb-3 leading-relaxed'>
                                          {review.review}
                                       </div>
                                    )}
                                    <div className='text-xs text-gray-500'>
                                       {format(new Date(review.createdAt), 'MMM dd, yyyy • hh:mm a')}
                                    </div>
                                 </div>
                              ))}
                           </div>
                        </div>
                     )}
                  </div>
               )}
            </ScrollArea>
         </SheetContent>
      </Sheet>
   );
}
