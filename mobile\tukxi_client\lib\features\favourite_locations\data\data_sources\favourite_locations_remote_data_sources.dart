import 'package:tukxi/features/favourite_locations/data/models/favourite_location_response.dart';

abstract class FavouriteRemoteDataSource {
  /// Fetch all favourite locations for the current user
  Future<FavoriteSuccessListResponse> getFavouriteLocations();

  /// Add a new favourite location
  Future<FavoriteSuccessResponse> addFavouriteLocation({
    required String name,
    required double latitude,
    required double longitude,
    required Map<String, dynamic> meta,
    String? description,
  });

  /// Edit an existing favourite location by its ID
  Future<FavoriteSuccessResponse> editFavouriteLocation({
    required String id,
    String? name,
    String? description,
    double? latitude,
    double? longitude,
    Map<String, dynamic>? meta,
  });

  /// Delete a favourite location by its ID
  Future<FavoriteSuccessResponse> deleteFavouriteLocation({required String id});
}
