import { Injectable } from '@nestjs/common';
import { PrismaService } from '../database/prisma/prisma.service';
import { BaseRepository } from './base.repository';
import { TaxSubcategory } from './models/taxGroup.model';

@Injectable()
export class TaxSubcategoryRepository extends BaseRepository<TaxSubcategory> {
  protected readonly modelName = 'taxSubcategory';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new tax subcategory record.
   * @param data - Tax subcategory data excluding id and timestamps
   */
  async createTaxSubcategory(
    data: Omit<TaxSubcategory, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<TaxSubcategory> {
    return this.create(data);
  }

  /**
   * Find subcategories by tax group ID.
   * @param taxGroupId - Tax group ID
   */
  async findSubcategoriesByTaxGroupId(
    taxGroupId: string,
  ): Promise<TaxSubcategory[]> {
    return this.findMany({
      where: { taxGroupId },
      orderBy: { createdAt: 'asc' },
    });
  }

  /**
   * Update tax subcategory by ID.
   * @param id - Tax subcategory ID
   * @param data - Updated tax subcategory data
   */
  async updateTaxSubcategory(
    id: string,
    data: Partial<
      Omit<TaxSubcategory, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>
    >,
  ): Promise<TaxSubcategory> {
    return this.update({
      where: { id },
      data,
    });
  }

  /**
   * Delete tax subcategory by ID (soft delete).
   * @param id - Tax subcategory ID
   */
  async deleteTaxSubcategory(id: string): Promise<TaxSubcategory> {
    return this.softDelete({ where: { id } });
  }

  /**
   * Delete all subcategories for a tax group.
   * @param taxGroupId - Tax group ID
   */
  async deleteSubcategoriesByTaxGroupId(taxGroupId: string): Promise<void> {
    await this.model.updateMany({
      where: {
        taxGroupId,
        deletedAt: null,
      },
      data: {
        deletedAt: new Date(),
        updatedAt: new Date(),
      },
    });
  }

  /**
   * Calculate total percentage for a tax group.
   * @param taxGroupId - Tax group ID
   */
  async calculateTotalPercentage(taxGroupId: string): Promise<number> {
    const result = await this.model.aggregate({
      where: {
        taxGroupId,
        deletedAt: null,
      },
      _sum: {
        percentage: true,
      },
    });

    return Number(result._sum.percentage) || 0;
  }

  /**
   * Create multiple subcategories in a transaction.
   * @param subcategories - Array of subcategory data
   */
  async createManySubcategories(
    subcategories: Omit<
      TaxSubcategory,
      'id' | 'createdAt' | 'updatedAt' | 'deletedAt'
    >[],
  ): Promise<TaxSubcategory[]> {
    const createdSubcategories: TaxSubcategory[] = [];

    for (const subcategory of subcategories) {
      const created = await this.createTaxSubcategory(subcategory);
      createdSubcategories.push(created);
    }

    return createdSubcategories;
  }
}
