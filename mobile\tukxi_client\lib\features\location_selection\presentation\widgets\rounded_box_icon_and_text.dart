import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/theme/app_colors.dart';

class RoundedBoxIconAndText extends StatefulWidget {
  const RoundedBoxIconAndText({
    super.key,
    required this.title,
    required this.assetPath,
  });

  final String assetPath;
  final String title;

  @override
  State<StatefulWidget> createState() {
    return _RoundedBoxIconAndTextState();
  }
}

class _RoundedBoxIconAndTextState extends State<RoundedBoxIconAndText> {
  @override
  Widget build(BuildContext context) {
    return FilterChip(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 10),
      label: Row(
        children: [
          Image.asset(widget.assetPath, height: 18, width: 18),

          const SizedBox(width: 5),

          Text(
            widget.title,
            style: GoogleFonts.inter(
              fontWeight: FontWeight.w500,
              fontSize: 12,
              color: Colors.black,
            ),
          ),
        ],
      ),
      onSelected: (bool value) {},
      selectedColor: Colors.white,
      backgroundColor: Colors.white,
      shape: StadiumBorder(
        side: BorderSide(color: AppColors.black10, width: 0.75),
      ),
      showCheckmark: false,
    );
  }
}
