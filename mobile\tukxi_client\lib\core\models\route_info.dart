import 'package:google_maps_flutter/google_maps_flutter.dart';

class RouteInfo {
  final List<LatLng> polylineCoordinates;
  final LatLngBounds bounds;
  // final String distanceText;
  // final int distanceValue; // in meters
  // final String durationText;
  // final int durationValue; // in seconds
  // final List<RouteStep> steps;

  RouteInfo({
    required this.polylineCoordinates,
    required this.bounds,
    // required this.distanceText,
    // required this.distanceValue,
    // required this.durationText,
    // required this.durationValue,
    // required this.steps,
  });
}

class RouteStep {
  final String instruction;
  final LatLng startLocation;
  final LatLng endLocation;

  RouteStep({
    required this.instruction,
    required this.startLocation,
    required this.endLocation,
  });
}
