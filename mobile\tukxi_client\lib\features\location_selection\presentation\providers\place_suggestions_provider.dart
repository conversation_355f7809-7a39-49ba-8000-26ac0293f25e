import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/core/services/google_place_service.dart';
import 'package:tukxi/features/location_selection/data/models/place_suggestion.dart';
import 'package:tukxi/features/location_selection/data/repositories/google_place_repository_impl.dart';
import 'package:tukxi/features/location_selection/domain/usecases/get_place_usecase.dart';
import 'package:tukxi/features/location_selection/presentation/state/place_suggestions_state.dart';
import 'package:tukxi/features/home/<USER>/params/location_params.dart';

/// Pickup location provider
final pickupLocationProvider = StateProvider<LocationParams?>((ref) => null);

/// Destination location provider
final destinationLocationProvider = StateProvider<LocationParams?>(
  (ref) => null,
);

final mapPaddingProvider = StateProvider<double>((ref) => 0.0);

final placeSuggestionsProvider =
    StateNotifierProvider<PlaceSuggestionsNotifier, PlaceSuggestionsState>((
      ref,
    ) {
      final service = GooglePlacesService();
      final repository = GooglePlacesRepositoryImpl(service: service);
      final useCase = GetPlaceUsecase(repository: repository);
      return PlaceSuggestionsNotifier(useCase);
    });

class PlaceSuggestionsNotifier extends StateNotifier<PlaceSuggestionsState> {
  PlaceSuggestionsNotifier(this.usecase) : super(PlaceSuggestionInitial());

  final GetPlaceUsecase usecase;

  Future<Either<Failure, List<PlaceSuggestion>>> getPlaceSuggestions({
    required String query,
    required LatLng? location,
  }) async {
    state = PlaceSuggestionLoading();

    final result = await usecase.executeGetPlaceSuggestion(
      query: query,
      location: location,
    );

    result.fold(
      (failure) {
        if (failure.type == ErrorType.noInternet) {
          state = PlaceSuggestionError('no_internet');
        } else {
          state = PlaceSuggestionError(failure.message);
        }
      },
      (response) {
        state = PlaceSuggestionSuccess(suggestions: response);
      },
    );
    return result;
  }

  Future<Either<Failure, LocationParams?>> getPlaceDetailsFromCoordinates(
    LatLng? coordinates,
  ) async {
    state = PlaceSuggestionLoading();

    final result = await usecase.executeGetPlaceDetailsFromCoordinates(
      coordinates,
    );
    result.fold(
      (failure) {
        if (failure.type == ErrorType.noInternet) {
          state = PlaceSuggestionError('no_internet');
        } else {
          state = PlaceSuggestionError(failure.message);
        }
      },
      (response) {
        state = PlaceSuggestionSuccess(placeDetails: response);
      },
    );
    return result;
  }

  Future<Either<Failure, LatLng?>> getCoordinatesFromPlaceID(
    String placeId,
  ) async {
    state = PlaceSuggestionLoading();

    final result = await usecase.executeGetPlaceCoordinates(placeId);
    result.fold(
      (failure) {
        if (failure.type == ErrorType.noInternet) {
          state = PlaceSuggestionError('no_internet');
        } else {
          state = PlaceSuggestionError(failure.message);
        }
      },
      (response) {
        state = PlaceSuggestionSuccess(latLng: response);
      },
    );
    return result;
  }

  Future<Either<Failure, LocationParams>> getAccurateLocation(
    LatLng? coordinates,
  ) async {
    state = PlaceSuggestionLoading();

    final result = await usecase.executeGetAccurateLocation(coordinates);
    result.fold(
      (failure) {
        if (failure.type == ErrorType.noInternet) {
          state = PlaceSuggestionError('no_internet');
        } else {
          state = PlaceSuggestionError(failure.message);
        }
      },
      (response) {
        state = PlaceSuggestionSuccess(placeDetails: response);
      },
    );
    return result;
  }
}
