import 'package:dartz/dartz.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/features/favourite_locations/data/data_sources/favourite_locations_remote_data_sources.dart';
import 'package:tukxi/features/favourite_locations/data/models/favourite_location.dart';
import 'package:tukxi/features/favourite_locations/data/models/favourite_location_response.dart';
import 'package:tukxi/features/favourite_locations/domain/repositories/favourite_location_repository.dart';

class FavouriteRepositoryImpl implements FavouriteRepository {
  FavouriteRepositoryImpl({required this.remoteDataSource});

  final FavouriteRemoteDataSource remoteDataSource;

  @override
  Future<Either<Failure, List<FavouriteLocation>>>
  getFavouriteLocations() async {
    return handleApiCall(() async {
      final response = await remoteDataSource.getFavouriteLocations();
      return response.data?.data ?? [];
    }, apiErrorMessage: 'Failed to fetch favourite locations.');
  }

  @override
  Future<Either<Failure, FavouriteLocation>> addFavouriteLocation({
    required String name,
    required double latitude,
    required double longitude,
    required Map<String, dynamic> meta,
    String? description,
  }) async {
    return handleApiCall(() async {
      final response = await remoteDataSource.addFavouriteLocation(
        name: name,
        latitude: latitude,
        longitude: longitude,
        meta: meta,
        description: description,
      );
      return response.data!;
    }, apiErrorMessage: 'Failed to add favourite location.');
  }

  @override
  Future<Either<Failure, FavouriteLocation>> editFavouriteLocation({
    required String id,
    String? name,
    String? description,
    double? latitude,
    double? longitude,
    Map<String, dynamic>? meta,
  }) async {
    return handleApiCall(() async {
      final response = await remoteDataSource.editFavouriteLocation(
        id: id,
        name: name,
        description: description,
        latitude: latitude,
        longitude: longitude,
        meta: meta,
      );
      return response.data!;
    }, apiErrorMessage: 'Failed to edit favourite location.');
  }

  @override
  Future<Either<Failure, FavoriteSuccessResponse>> deleteFavouriteLocation({
    required String id,
  }) async {
    return handleApiCall(
      () => remoteDataSource.deleteFavouriteLocation(id: id),
      apiErrorMessage: 'Failed to delete favourite location.',
    );
  }
}
