import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/services/life_cycle_service.dart';
import 'package:tukxi/features/booking/data/models/ride_status_event.dart';

final lifeCycleServiceProvider = Provider<LifeCycleService>((ref) {
  final service = LifeCycleService();

  // Auto-dispose when no longer needed
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Latest ride status event for the currently viewed ride.
final rideStatusProvider =
    StateNotifierProvider<RideStatusNotifier, RideStatusEvent?>(
      (ref) => RideStatusNotifier(),
    );

class RideStatusNotifier extends StateNotifier<RideStatusEvent?> {
  RideStatusNotifier() : super(null);
  void update(RideStatusEvent event) {
    state = event;
    final status = RideStatusType.fromString(event.status ?? '');

    if (status == RideStatusType.cancelled ||
        status == RideStatusType.failed ||
        status == RideStatusType.completed) {
      // Clear the state after 5 seconds for terminal statuses
      Future.delayed(const Duration(seconds: 5), () {
        clear();
      });
    }
  }

  void clear() => state = null;
}
