import { Module } from '@nestjs/common';
import { HealthModule } from './health/health.module';
import { ApiAuthModule } from './auth/auth.module';
import { ApiCountryModule } from './country/country.module';
import { ApiCityModule } from './city/city.module';
// import { ApiDriverOnboardingModule } from './driver-onboarding/driver-onboarding.module';
import { ApiLanguageModule } from './language/language.module';
import { UserProfileModule as ApiUserProfileModule } from './user-profile/user-profile.module';
import { ApiFileUploadModule } from './file-upload/file-upload.modue';
import { ApiVehicleTypeModule } from './vehicle-type/vehicle-type.module';
import { ApiDriverVehicleModule } from './driver-vehicle/driver-vehicle.module';
import { ApiProductModule } from './product/product.module';
import { ApiKycDocumentModule } from './kyc-document/kyc-document.module';
import { ApiDriverKycModule } from './driver-kyc/driver-kyc.module';
import { ApiDriverModule } from './admin/driver/driver.module';
import { ApiProductServiceModule } from './product-service/product-service.module';
import { ApiCityProductModule } from './city-product/city-product.module';
import { ApiDriverCityProductModule } from './driver-city-product/driver-city-product.module';
import { ApiDriverVehicleDocumentModule } from './admin/driver-vehicle-document/driver-vehicle-document.module';
import { ApiRideModule } from './rides/ride.module';
import { ApiFavoriteLocationModule } from './favorite-location/favorite-location.module';
import { RealtimeModule } from './realtime/realtime.module';
import { ApiZoneTypeModule } from './zone-type/zone-type.module';
import { ApiZoneModule } from './zone/zone.module';
import { ApiReviewModule } from './reviews/review.module';
import { ApiRoleModule } from './roles/role.module';
import { PermissionsModule } from './permissions/permissions.module';
import { SubAdminModule } from './admin/sub-admin/sub-admin.module';
import { ApiChargeGroupModule } from './charge-group/charge-group.module';
import { ApiCityProductFareModule } from './city-product-fare/city-product-fare.module';
import { ApiTaxGroupsModule } from './tax-groups/tax-groups.module';

@Module({
  imports: [
    HealthModule,
    ApiCountryModule,
    ApiCityModule,
    ApiAuthModule,
    ApiVehicleTypeModule,
    ApiLanguageModule,
    ApiUserProfileModule,
    ApiFileUploadModule,
    ApiDriverVehicleModule,
    ApiProductModule,
    ApiKycDocumentModule,
    ApiDriverKycModule,
    ApiDriverModule,
    ApiProductServiceModule,
    ApiCityProductModule,
    ApiDriverCityProductModule,
    ApiDriverVehicleDocumentModule,
    RealtimeModule,
    ApiRideModule,
    ApiFavoriteLocationModule,
    ApiZoneTypeModule,
    ApiZoneModule,
    ApiReviewModule,
    ApiRoleModule,
    PermissionsModule,
    SubAdminModule,
    ApiChargeGroupModule,
    ApiCityProductFareModule,
    ApiTaxGroupsModule,
  ],
})
export class V1Module {}
