import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { MongoAbility } from '@casl/ability';
import { ApiConsumer } from '@shared/shared/modules/auth/interfaces';

// Import all ability factories
import { CityAbilityFactory } from '@shared/shared/casl/ability-factories/city-ability.factory';
import { CityAdminAbilityFactory } from '@shared/shared/casl/ability-factories/city-admin-ability.factory';
import { CountryAbilityFactory } from '@shared/shared/casl/ability-factories/country-ability.factory';
import { LanguageAbilityFactory } from '@shared/shared/casl/ability-factories/language-ability.factory';
import { ProductAbilityFactory } from '@shared/shared/casl/ability-factories/product-ability.factory';
import { ProductServiceAbilityFactory } from '@shared/shared/casl/ability-factories/product-service-ability.factory';
import { RolesAbilityFactory } from '@shared/shared/casl/ability-factories/roles-ability.factory';
import { SubAdminAbilityFactory } from '@shared/shared/casl/ability-factories/sub-admin-ability.factory';
import { VehicleCategoryAbilityFactory } from '@shared/shared/casl/ability-factories/vehicle-category-ability.factory';
import { ZoneTypeAbilityFactory } from '@shared/shared/casl/ability-factories/zone-type-ability.factory';
// Import repositories
import { CityAdminRepository } from '@shared/shared/repositories/city-admin.repository';

type Action =
  | 'create'
  | 'list'
  | 'edit'
  | 'delete'
  | 'manage'
  | 'status_update';

export type AppAbility = MongoAbility<[Action, any]>;

@Injectable()
export class CaslAbilityGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private cityAdminRepository: CityAdminRepository,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // 1. Extract the request and apiConsumer from the context
    const request = context.switchToHttp().getRequest();
    const apiConsumer: ApiConsumer = request.user;
    console.log('API Consumer:', apiConsumer);
    if (!apiConsumer) {
      throw new ForbiddenException('User not authenticated');
    }

    // 2. Define ability factories for different entities
    const abilityFactories: Record<
      string,
      {
        factory: any;
        entity: string;
        subject?: any;
      }
    > = {
      city: {
        factory: new CityAbilityFactory(this.cityAdminRepository),
        entity: 'City',
      },
      country: {
        factory: new CountryAbilityFactory(),
        entity: 'Country',
      },
      language: {
        factory: new LanguageAbilityFactory(),
        entity: 'Language',
      },
      product: {
        factory: new ProductAbilityFactory(),
        entity: 'Product',
      },
      productService: {
        factory: new ProductServiceAbilityFactory(),
        entity: 'ProductService',
      },
      role: {
        factory: new RolesAbilityFactory(),
        entity: 'Role',
      },
      subAdmin: {
        factory: new SubAdminAbilityFactory(),
        entity: 'SubAdmin',
      },
      cityAdmin: {
        factory: new CityAdminAbilityFactory(),
        entity: 'CityAdmin',
      },
      vehicleCategory: {
        factory: new VehicleCategoryAbilityFactory(),
        entity: 'VehicleCategory',
      },
      zoneType: {
        factory: new ZoneTypeAbilityFactory(),
        entity: 'ZoneType',
      },
    };

    // 3. Get required ability action from metadata on the handler
    const abilityAction = this.reflector.get<string>(
      'ability',
      context.getHandler(),
    );

    if (!abilityAction) {
      throw new ForbiddenException('Ability action not found');
    }

    // abilityAction format: "abilityName:action"
    const [abilityName, action] = abilityAction.split(':');
    console.log('Ability name:', abilityName, 'Action:', action);

    // 4. Get the matching ability factory
    const abilityFactory =
      abilityFactories[abilityName as keyof typeof abilityFactories];
    if (!abilityFactory) {
      throw new ForbiddenException('Invalid ability factory name');
    }

    // 5. Create ability for this API consumer
    const ability =
      await abilityFactory.factory.createForAPIConsumer(apiConsumer);

    if (!ability) {
      throw new ForbiddenException(
        'You do not have permission to perform this action',
      );
    }

    // 6. Decide what subject (entity instance or entity type) to check
    let subject: any = abilityFactory.entity;

    // For entities that have specific subjects, try to get the subject instance
    if (abilityFactory.subject !== undefined) {
      try {
        subject = await abilityFactory.subject;
      } catch (error) {
        throw new ForbiddenException(`${abilityName} not found`);
      }
    }

    // 7. Check if ability allows the action on the subject
    if (ability.cannot(action as Action, subject)) {
      throw new ForbiddenException(
        'You do not have permission to perform this action',
      );
    }

    // 8. If all good, allow the request
    return true;
  }
}
