import 'package:tukxi/features/profile/domain/entities/file_upload_entity.dart';

class FileUploadResponse extends FileUploadEntity {
  FileUploadResponse({super.success, super.message, super.data});

  factory FileUploadResponse.fromJson(Map<String, dynamic> json) {
    return FileUploadResponse(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      data: json['data'] != null
          ? FileUploadData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message, 'data': data?.toJson()};
  }
}

class FileUploadData extends FileUploadDataEntity {
  FileUploadData({super.contentType, super.url, super.key, super.size});

  factory FileUploadData.fromJson(Map<String, dynamic> json) {
    return FileUploadData(
      contentType: json['contentType'] as String? ?? '',
      url: json['url'] as String? ?? '',
      key: json['key'] as String? ?? '',
      size: json['size'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {'contentType': contentType, 'url': url, 'key': key, 'size': size};
  }
}
