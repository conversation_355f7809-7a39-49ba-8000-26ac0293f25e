import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/features/favourite_locations/data/models/favourite_location.dart';

class FavouriteLocationCard extends StatelessWidget {
  const FavouriteLocationCard({
    super.key,
    required this.address,
    required this.onOptionsTapped,
  });

  final FavouriteLocation address;
  final void Function(FavouriteLocation address) onOptionsTapped;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Circle avatar placeholder
        Container(
          width: 35,
          height: 35,
          decoration: BoxDecoration(
            color: AppColors.primary10,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Image.asset(
              _fetchAssetpath(address.meta?.type ?? 'other'),
              width: 12,
              color: AppColors.primary,
            ),
          ),
        ),
        const SizedBox(width: 12),
        // Title + Subtitle
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                address.name ?? '',
                style: GoogleFonts.inter(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 5),
              Text(
                '${address.description ?? ''}, ${address.meta?.address ?? ''}',
                style: GoogleFonts.inter(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: AppColors.black50,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(width: 8),
        SizedBox(
          width: 35,
          child: IconButton(
            onPressed: () {
              onOptionsTapped(address);
            },
            icon: Image.asset(AssetPaths.dots),
          ),
        ),
      ],
    );
  }

  String _fetchAssetpath(String type) {
    if (type.toLowerCase() ==
        FavouriteAddressType.home.value.title.toLowerCase()) {
      return AssetPaths.homeLoc;
    } else if (type == FavouriteAddressType.work.value.title) {
      return AssetPaths.workLoc;
    } else {
      return AssetPaths.otherLoc;
    }
  }
}
