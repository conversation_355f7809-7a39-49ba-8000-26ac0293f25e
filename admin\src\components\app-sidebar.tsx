'use client';

import { usePathname } from 'next/navigation';
import * as React from 'react';

import { NavMain } from '@/components/nav-main';
import { Sidebar, SidebarContent, SidebarHeader, SidebarRail } from '@/components/ui/sidebar';
import { Skeleton } from '@/components/ui/skeleton';
import { SIDEBAR_ROUTES } from '@/data/sidebar-routes';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { LogoName } from './logo-name';

// Sidebar skeleton loader component
function SidebarSkeleton() {
   return (
      <div className='py-2 group-data-[collapsible=icon]:px-1'>
         <div className='space-y-1'>
            {Array.from({ length: 9 }).map((_, index) => (
               <div
                  key={index}
                  className='h-8 px-3 py-1 rounded-md flex items-center gap-2 group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:mx-auto group-data-[collapsible=icon]:w-8'
               >
                  <Skeleton className='w-4 h-4 flex-shrink-0' />
                  <Skeleton className='h-3.5 flex-1 group-data-[collapsible=icon]:hidden' />
               </div>
            ))}
         </div>
      </div>
   );
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
   const pathname = usePathname();
   const [isClient, setIsClient] = React.useState(false);
   const [data, setData] = React.useState<
      Array<{
         title: string;
         url: string;
         icon: any;
         isActive: boolean;
         pagePermission: any;
      }>
   >([]);

   React.useEffect(() => {
      setIsClient(true);
      setData(SIDEBAR_ROUTES);
   }, []);

   const { hasAnyPermission, isLoading } = useRoleBasedAccess();

   // Update navMain items with active state based on current pathname
   const navMenuWithActiveState = data.map(item => ({
      ...item,
      isActive: pathname === item.url || pathname.startsWith(item.url + '/'),
   }));

   const navMainWithActiveState = React.useMemo(
      () =>
         !isClient || isLoading || data.length === 0
            ? []
            : navMenuWithActiveState.filter(menu => {
                 const allPagePermissions = Object.keys(menu.pagePermission).map(
                    pagePermission => (menu.pagePermission as any)[pagePermission]
                 );

                 //   for a page to be visible in sidebar, it must have atleast one of that pages permission
                 return hasAnyPermission(allPagePermissions);
              }),
      [hasAnyPermission, isLoading, navMenuWithActiveState, data.length, isClient]
   );

   return (
      <Sidebar className='bg-white border-r border-gray-200' collapsible='icon' {...props}>
         <SidebarHeader className='bg-white border-b border-gray-200 p-3 group-data-[collapsible=icon]:p-1 group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:items-center group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:h-12'>
            <div className='flex items-center justify-center'>
               <LogoName width={60} height={60} />
            </div>
         </SidebarHeader>
         <SidebarContent className='bg-white group-data-[collapsible=icon]:p-1'>
            {!isClient || isLoading ? (
               <SidebarSkeleton />
            ) : (
               <NavMain items={navMainWithActiveState} />
            )}
         </SidebarContent>
         <SidebarRail />
      </Sidebar>
   );
}
