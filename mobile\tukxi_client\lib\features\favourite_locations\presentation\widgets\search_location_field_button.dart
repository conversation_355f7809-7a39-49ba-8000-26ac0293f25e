import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/theme/app_colors.dart';

class SearchLocationFieldButton extends StatelessWidget {
  const SearchLocationFieldButton({super.key, required this.searchTapped});

  final VoidCallback searchTapped;

  @override
  Widget build(BuildContext context) {
    return FilledButton(
      style: FilledButton.styleFrom(
        backgroundColor: Colors.white,
        padding: const EdgeInsets.only(left: 10, top: 12, bottom: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(color: AppColors.black20),
        ),
      ),
      onPressed: searchTapped,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgPicture.asset(
            AssetPaths.search,
            height: 20,
            width: 20,
            colorFilter: const ColorFilter.mode(
              AppColors.black50,
              BlendMode.srcIn,
            ),
          ),

          const SizedBox(width: 15),

          Text(
            'Search for a building, street name or area',
            style: GoogleFonts.inter(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: AppColors.black50,
            ),
          ),
        ],
      ),
    );
  }
}
