import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/features/booking/data/models/driver.dart';
import 'package:tukxi/features/booking/data/models/product.dart';
import 'package:tukxi/features/booking/data/models/ride.dart';
import 'package:tukxi/features/favourite_locations/data/models/location_response.dart';
import 'package:tukxi/features/trips/data/models/trip_history.dart';

class TripHistoryEntity {
  const TripHistoryEntity({
    this.success,
    this.message,
    this.data,
    this.meta,
    this.timestamp,
  });

  final bool? success;
  final String? message;
  final List<Trip>? data;
  final TripMeta? meta;
  final int? timestamp;
}

class TripResponseEntity {
  const TripResponseEntity({
    required this.success,
    required this.message,
    required this.trip,
    required this.timestamp,
  });

  final bool success;
  final String message;
  final Trip? trip;
  final int timestamp;
}

class TripEntity {
  const TripEntity({
    required this.id,
    this.status,
    this.rating,
    this.pickupLocation,
    this.destinationLocation,
    this.stops,
    this.createdAt,
    this.completedAt,
    this.product,
    this.driver,
    this.driverVehicle,
    this.rider,
  });

  final String id;
  final RideRating? rating;
  final RideStatusType? status;
  final LocationResponse? pickupLocation;
  final LocationResponse? destinationLocation;
  final List<LocationResponse>? stops;
  final DateTime? createdAt;
  final DateTime? completedAt;
  final Product? product;
  final Driver? driver;
  final DriverVehicle? driverVehicle;
  final Rider? rider;
}

class TripMetaEntity {
  const TripMetaEntity({
    required this.page,
    required this.limit,
    required this.total,
    required this.totalPages,
  });

  final int page;
  final int limit;
  final int total;
  final int totalPages;
}

class RideRatingEntity {
  const RideRatingEntity({
    required this.id,
    required this.riderId,
    required this.driverId,
    required this.rideId,
    required this.reviewById,
    required this.rating,
    this.review,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
  });

  final String? id;
  final String? riderId;
  final String? driverId;
  final String? rideId;
  final String? reviewById;
  final String? rating;
  final String? review;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
}
