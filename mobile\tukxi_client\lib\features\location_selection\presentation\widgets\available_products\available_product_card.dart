import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/extensions/date_extensions.dart';
import 'package:tukxi/core/extensions/int_extensions.dart';
import 'package:tukxi/core/extensions/string_extensions.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/widgets/remote_image_widget.dart';
import 'package:tukxi/features/location_selection/data/models/available_option.dart';

class AvailableProductCard extends StatefulWidget {
  const AvailableProductCard({
    super.key,
    required this.ride,
    required this.index,
    required this.selectedIndex,
    required this.onAvailableOptionSelected,
  });

  final AvailableOptionData ride;
  final int selectedIndex;
  final int index;
  final void Function(int selectedIndex) onAvailableOptionSelected;

  @override
  State<AvailableProductCard> createState() {
    return _AvailableProductCardState();
  }
}

class _AvailableProductCardState extends State<AvailableProductCard> {
  static const double _size = 60;
  @override
  Widget build(BuildContext context) {
    final serviceName =
        fromTabBarItem(widget.ride.serviceName ?? '') ?? ServiceName.intercity;

    return InkWell(
      onTap: () {
        widget.onAvailableOptionSelected(widget.index);
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppColors.greyBg,
          borderRadius: BorderRadius.circular(12),
          border: widget.selectedIndex == widget.index
              ? Border.all(color: AppColors.primary, width: 2)
              : Border.all(color: Colors.transparent, width: 0),
        ),
        child: Row(
          children: [
            if (widget.ride.icon != null && widget.ride.icon!.isNotEmpty)
              SizedBox(
                width: _size,
                height: _size,
                child: RemoteImageWidget(
                  imageUrl: widget.ride.icon!,
                  size: _size,
                  borderRadius: 0,
                  fit: BoxFit.contain,
                ),
              )
            else
              _buildDefaultImage(serviceName),

            const SizedBox(width: 5),

            _buildRideDetails(),

            const SizedBox(width: 12),

            const SizedBox(width: 10),
            _buildPriceSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultImage(ServiceName serviceName) {
    return Image.asset(
      _fetchImageUsingServiceName(serviceName),
      width: 60,
      height: 60,
      fit: BoxFit.contain,
    );
  }

  Widget _buildRideDetails() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Flexible(
                flex: 2,
                child: Text(
                  widget.ride.name ?? '',
                  softWrap: true,
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(width: 8),

              SvgPicture.asset(
                AssetPaths.personal,
                height: 14,
                colorFilter: const ColorFilter.mode(
                  AppColors.black50,
                  BlendMode.srcIn,
                ),
              ),
              const SizedBox(width: 3),
              Text(
                '${widget.ride.passengerLimit ?? 0}',
                style: GoogleFonts.inter(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: AppColors.black50,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            etaText(),
            style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          '₹${widget.ride.price?.toStringAsFixed(2) ?? 0.00}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        Text(
          '₹${widget.ride.strikethroughPrice?.toStringAsFixed(2) ?? 0.00}',
          style: const TextStyle(
            color: Colors.grey,
            decoration: TextDecoration.lineThrough,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  String _fetchImageUsingServiceName(ServiceName serviceName) {
    switch (serviceName) {
      case ServiceName.auto:
        return AssetPaths.rideAuto;
      case ServiceName.intercity:
        return AssetPaths.intercity;
      case ServiceName.rental:
        return AssetPaths.rideRental;
      case ServiceName.sedan:
        return AssetPaths.rideSedan;
      case ServiceName.suv:
        return AssetPaths.rideSuv;
    }
  }

  static ServiceName? fromTabBarItem(String serviceName) {
    return ServiceName.values.firstWhere(
      (item) => item.name.toLowerCase() == serviceName.toLowerCase(),
      orElse: () => ServiceName.intercity,
    );
  }

  String etaText() {
    final eta = widget.ride.driverToPickupResults?.durationInSeconds ?? 0;
    final time =
        widget.ride.driverToPickupResults?.estimatedArrivalTime ?? '0.0';

    final etaTimeFormat = eta.asTimeFormat;
    return (eta == 0 || int.tryParse(time) == 0)
        ? 'Expects delay'
        : (etaTimeFormat == '0'
              ? 'in <1m • ${time.toDateTime()?.toSmartString}'
              : 'in ${eta.asTimeFormat} • ${time.toDateTime()?.toSmartString}');
  }
}
