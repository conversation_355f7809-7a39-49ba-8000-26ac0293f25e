model CityProductFare {
  id             String    @id @default(uuid()) @map("id") @db.Uuid
  cityProductId  String    @map("city_product_id") @db.Uuid
  priority       Int       @map("priority")
  status         String    @default("active") @map("status")
  fromZoneId     String?   @map("from_zone_id") @db.Uuid
  toZoneId       String?   @map("to_zone_id") @db.Uuid
  fromZoneTypeId String?   @map("from_zone_type_id") @db.Uuid
  toZoneTypeId   String?   @map("to_zone_type_id") @db.Uuid
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  cityProduct      CityProduct?      @relation(fields: [cityProductId], references: [id])
  fromZone         Zone?             @relation("FromZoneFares", fields: [fromZoneId], references: [id])
  toZone           Zone?             @relation("ToZoneFares", fields: [toZoneId], references: [id])
  fromZoneType     ZoneType?         @relation("FromZoneTypeFares", fields: [fromZoneTypeId], references: [id])
  toZoneType       ZoneType?         @relation("ToZoneTypeFares", fields: [toZoneTypeId], references: [id])
  fareChargeGroups FareChargeGroup[]

  @@index([cityProductId], name: "idx_city_product_fare_city_product_id")
  @@index([priority], name: "idx_city_product_fare_priority")
  @@index([status], name: "idx_city_product_fare_status")
  @@index([fromZoneId], name: "idx_city_product_fare_from_zone_id")
  @@index([toZoneId], name: "idx_city_product_fare_to_zone_id")
  @@index([fromZoneTypeId], name: "idx_city_product_fare_from_zone_type_id")
  @@index([toZoneTypeId], name: "idx_city_product_fare_to_zone_type_id")
  @@map("city_product_fares")
}

model FareChargeGroup {
  id                String    @id @default(uuid()) @map("id") @db.Uuid
  cityProductFareId String    @map("city_product_fare_id") @db.Uuid
  chargeGroupId     String    @map("charge_group_id") @db.Uuid
  priority          Int       @map("priority")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  deletedAt         DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  cityProductFare CityProductFare @relation(fields: [cityProductFareId], references: [id])
  chargeGroup     ChargeGroup     @relation(fields: [chargeGroupId], references: [id])

  @@unique([cityProductFareId, chargeGroupId])
  @@index([cityProductFareId], name: "idx_fare_charge_group_city_product_fare_id")
  @@index([chargeGroupId], name: "idx_fare_charge_group_charge_group_id")
  @@index([priority], name: "idx_fare_charge_group_priority")
  @@map("fare_charge_groups")
}
