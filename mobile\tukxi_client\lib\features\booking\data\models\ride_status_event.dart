import 'package:tukxi/features/booking/data/models/driver.dart';
import 'package:tukxi/features/booking/data/models/ride_metadata.dart';

class RideStatusEvent {
  final String? rideId;
  final String? riderId;
  final String? status;
  final String? message;
  final DateTime? timestamp;
  final RideMetadata? metadata;
  final Driver? driver;

  RideStatusEvent({
    this.rideId,
    this.riderId,
    this.status,
    this.message,
    this.timestamp,
    this.driver,
    this.metadata,
  });

  factory RideStatusEvent.fromJson(Map<String, dynamic> json) {
    return RideStatusEvent(
      rideId: json['rideId'] as String? ?? '',
      riderId: json['riderId'] as String? ?? '',
      status: /*RideStatusType.fromString(*/
          json['status'] as String? ?? '' /*)*/,
      message: json['message'] as String? ?? '',
      timestamp: json['timestamp'] == null
          ? null
          : DateTime.parse(json['timestamp'] as String? ?? ''),
      metadata: json['metadata'] != null
          ? RideMetadata.fromJson(json['metadata'])
          : null,
      driver: json['driver'] == null ? null : Driver.fromJson(json['driver']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'rideId': rideId,
      'riderId': riderId,
      'status': status,
      'message': message,
      'timestamp': timestamp?.toIso8601String(),
      'metadata': metadata?.toJson(),
    };
  }
}
