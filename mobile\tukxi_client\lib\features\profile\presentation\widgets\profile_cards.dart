import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/features/profile/data/models/profile_details.dart';

class ProfileCards extends StatelessWidget {
  const ProfileCards({super.key, required this.profileList});

  final List<ProfileDetails> profileList;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (ctx, constraints) {
        final spacing = 16.0;
        final totalSpacing = spacing * 2;
        final maxWidth = constraints.maxWidth;
        final cardWidth = (maxWidth - totalSpacing) / 3;

        return IntrinsicHeight(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: profileList.asMap().entries.map((entry) {
              final idx = entry.key;
              final details = entry.value;
              final isLast = idx == profileList.length - 1;

              return _buildCard(
                cardWidth: cardWidth,
                details: details,
                isLast: isLast,
                spacing: spacing,
              );
            }).toList(),
          ),
        );
      },
    );
  }

  Widget _buildCard({
    required double cardWidth,
    required ProfileDetails details,
    required bool isLast,
    required double spacing,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 16),
          width: cardWidth,
          decoration: BoxDecoration(
            color: AppColors.profileCardGreyBg,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    details.value,
                    textAlign: TextAlign.center,
                    style: GoogleFonts.inter(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (isLast)
                    Row(
                      children: [
                        SizedBox(width: 3),
                        Image.asset(
                          AssetPaths.ratingStar,
                          width: 16,
                          color: AppColors.ratingOn,
                        ),
                      ],
                    ),
                ],
              ),
              SizedBox(height: 10),
              Text(
                details.title,
                textAlign: TextAlign.center,
                style: GoogleFonts.inter(
                  fontSize: 10,
                  color: AppColors.black50,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),

        if (!isLast) SizedBox(width: spacing),
      ],
    );
  }
}
