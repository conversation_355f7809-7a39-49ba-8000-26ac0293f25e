import 'package:dartz/dartz.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/features/booking/data/data_sources/ride_remote_data_source.dart';
import 'package:tukxi/features/booking/data/models/review_request.dart';
import 'package:tukxi/features/booking/data/models/review_response.dart';
import 'package:tukxi/features/booking/data/models/ride_request.dart';
import 'package:tukxi/features/booking/data/models/ride_response.dart';
import 'package:tukxi/features/booking/domain/repositories/ride_repository.dart';

class RideRepositoryImpl implements RideRepository {
  final RideRemoteDataSource dataSource;

  RideRepositoryImpl({required this.dataSource});

  @override
  Future<Either<Failure, RideResponse>> rideRequest(RideRequest request) {
    return handleApiCall(
      () => dataSource.rideRequest(request),
      apiErrorMessage: 'Failed to create ride.',
    );
  }

  @override
  Future<Either<Failure, RideResponse>> cancelRide(String rideId) {
    return handleApiCall(
      () => dataSource.cancelRide(rideId),
      apiErrorMessage: 'Failed to cancel ride.',
    );
  }

  @override
  Future<Either<Failure, RideListResponse>> fetchAllRides() {
    return handleApiCall(
      () => dataSource.fetchAllRides(),
      apiErrorMessage: 'Failed to fetch active rides.',
    );
  }

  @override
  Future<Either<Failure, RideResponse>> fetchRideDetails(String rideId) {
    return handleApiCall(
      () => dataSource.fetchRideDetails(rideId),
      apiErrorMessage: 'Failed to fetch ride details.',
    );
  }

  @override
  Future<Either<Failure, ReviewResponse>> reviewDriver(ReviewRequest request) {
    return handleApiCall(
      () => dataSource.reviewDriver(request),
      apiErrorMessage: 'Failed to review driver.',
    );
  }
}
