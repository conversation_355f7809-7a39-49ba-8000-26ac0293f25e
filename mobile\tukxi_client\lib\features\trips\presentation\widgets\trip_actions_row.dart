import 'package:flutter/material.dart';
import 'package:flutter_pannable_rating_bar/flutter_pannable_rating_bar.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/utils/snack_bar_utils.dart';
import 'package:tukxi/features/trips/presentation/widgets/trip_action_button.dart';

class TripActionsRow extends StatelessWidget {
  const TripActionsRow({
    super.key,
    required this.rating,
    required this.status,
    required this.onRateDriverTapped,
    required this.onRebookTapped,
  });

  final double? rating;
  final RideStatusType status;
  final VoidCallback onRateDriverTapped;
  final VoidCallback onRebookTapped;

  static const double itemHeight = 38;

  @override
  Widget build(BuildContext context) {
    return Wrap(
      direction: Axis.horizontal,
      alignment: WrapAlignment.center,
      spacing: 10,
      runSpacing: 12,
      children: [
        if (status == RideStatusType.completed)
          TripActionButton(
            itemHeight: itemHeight,
            image: AssetPaths.receipt,
            label: 'Receipt',
            onTap: () {
              if (status == RideStatusType.completed) {
                // Handle receipt action
              } else {
                SnackbarUtils.showSnackBar(
                  context: context,
                  message: 'No receipt available for this ride',
                  type: SnackBarType.error,
                );
              }
            },
          ),
        if (status == RideStatusType.completed) _buildRatingSection(),

        TripActionButton(
          itemHeight: itemHeight,
          image: AssetPaths.rideRebook,
          label: 'Rebook',
          onTap: onRebookTapped,
        ),
      ],
    );
  }

  Widget _buildRatingSection() {
    if (rating != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          PannableRatingBar(
            rate: rating ?? 0.0,
            alignment: WrapAlignment.center,
            crossAxisAlignment: WrapCrossAlignment.center,
            gestureType: GestureType.tapOnly,
            valueTransformer: halfFractionalValueTransformer,
            items: List.generate(
              5,
              (index) => RatingWidget(
                selectedColor: AppColors.ratingOn,
                unSelectedColor: AppColors.ratingOff,
                child: SizedBox(
                  width: 15,
                  height: 38,
                  child: Image.asset(
                    AssetPaths.ratingStar,
                    height: 15,
                    width: 15,
                  ),
                ),
              ),
            ),
          ),
          Text(
            ' $rating',
            style: GoogleFonts.inter(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: AppColors.black60,
            ),
          ),
        ],
      );
    } else {
      return TripActionButton(
        itemHeight: itemHeight,
        image: AssetPaths.rideRate,
        label: 'Rate driver',
        onTap: onRateDriverTapped,
      );
    }
  }
}
