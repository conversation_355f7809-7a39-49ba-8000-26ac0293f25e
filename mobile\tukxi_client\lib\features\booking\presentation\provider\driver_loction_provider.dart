import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Latest driver location for the currently viewed ride.
/// Structure: {"lat": double, "lng": double, ...}
final driverLocationProvider =
    StateNotifierProvider<DriverLocationNotifier, LatLng?>(
      (ref) => DriverLocationNotifier(),
    );

class DriverLocationNotifier extends StateNotifier<LatLng?> {
  DriverLocationNotifier() : super(null);
  void update(LatLng loc) => state = loc;
  void clear() => state = null;
}
