enum ZoneAlgorithm {
  CITY
  AIRPORT
  HIGHWAY
  SUBURBAN

  @@map("zone_algorithm")
}

model ZoneType {
  id          String        @id @default(uuid()) @map("id") @db.Uuid
  name        String        @unique @map("name")
  description String?       @map("description")
  algorithm   ZoneAlgorithm @default(CITY) @map("algorithm")
  config      Json?         @map("config") @db.JsonB // Algorithm-specific configuration
  isActive    Boolean       @default(true) @map("is_active")
  createdAt   DateTime      @default(now()) @map("created_at")
  updatedAt   DateTime      @updatedAt @map("updated_at")
  deletedAt   DateTime?     @map("deleted_at") @db.Timestamptz

  // Relations
  zones             Zone[]
  FromZoneTypeFares CityProductFare[] @relation("FromZoneTypeFares")
  ToZoneTypeFares   CityProductFare[] @relation("ToZoneTypeFares")

  @@index([algorithm], name: "idx_zone_type_algorithm")
  @@index([isActive], name: "idx_zone_type_is_active")
  @@map("zone_types")
}
