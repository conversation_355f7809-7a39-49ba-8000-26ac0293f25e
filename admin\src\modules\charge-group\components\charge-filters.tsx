'use client';

import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, Plus, Link } from 'lucide-react';

interface ChargeFiltersProps {
  search: string;
  onSearchChange: (value: string) => void;
  isLoading?: boolean;
  onAddCharge?: () => void;
  onAttachCharge?: () => void;
}

export function ChargeFilters({ search, onSearchChange, isLoading, onAddCharge, onAttachCharge }: ChargeFiltersProps) {
  return (
     <div className='flex justify-between items-center gap-4 mb-4'>
        <div className='relative flex-1 max-w-md'>
           <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
           <Input
              type='text'
              placeholder='Search charges...'
              value={search}
              onChange={e => onSearchChange(e.target.value)}
              disabled={isLoading}
              className='pl-10'
           />
        </div>
        <div className='flex gap-2'>
           {onAttachCharge && (
              <Button className='cursor-pointer' variant='outline' onClick={onAttachCharge}>
                 <Link className='h-4 w-4' />
                 Add Existing Charge
              </Button>
           )}
           {onAddCharge && (
              <Button className='cursor-pointer' variant='outline' onClick={onAddCharge}>
                 <Plus />
                 Add New Charge
              </Button>
           )}
        </div>
     </div>
  );
}