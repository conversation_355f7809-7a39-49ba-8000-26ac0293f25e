'use client';

import { useState } from 'react';
import { useListRides } from '../api/queries';
import { RideTable } from '../components/ride-table';
import { RideFilters } from '../components/ride-filters';

export function RidePage() {
   const [page, setPage] = useState(1);
   const [limit] = useState(9);
   const [status, setStatus] = useState<string>('');
   const [fromDate, setFromDate] = useState<string>('');
   const [toDate, setToDate] = useState<string>('');
   const [searchQuery, setSearchQuery] = useState<string>('');

   const listRides = useListRides({
      page,
      limit,
      status: status && status !== 'all' ? status : undefined,
      fromDate: fromDate || undefined,
      toDate: toDate || undefined,
      riderName: searchQuery || undefined,
      driverName: searchQuery || undefined,
      vehicleNumber: searchQuery || undefined,
   });

   const handleClearFilters = () => {
      setStatus('');
      setFromDate('');
      setToDate('');
      setSearchQuery('');
      setPage(1);
   };

   return (
      <div className='flex flex-1 flex-col gap-4 p-6'>
         <div className='flex justify-between items-center'>
            <h2 className='text-2xl font-semibold text-gray-900'>Rides</h2>
         </div>

         <RideFilters
            status={status}
            fromDate={fromDate}
            toDate={toDate}
            searchQuery={searchQuery}
            onStatusChange={setStatus}
            onFromDateChange={setFromDate}
            onToDateChange={setToDate}
            onSearchQueryChange={setSearchQuery}
            onClearFilters={handleClearFilters}
         />

         <RideTable
            data={listRides.data}
            isLoading={listRides.isLoading}
            currentPage={page}
            onPageChange={(newPage: number) => setPage(newPage)}
         />
      </div>
   );
}
