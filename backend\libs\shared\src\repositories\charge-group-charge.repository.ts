import { Injectable } from '@nestjs/common';
import { PrismaService } from '../database/prisma/prisma.service';
import { BaseRepository } from './base.repository';

export interface ChargeGroupCharge {
  id: string;
  chargeGroupId: string;
  chargeId: string;
  priority: number;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
}

@Injectable()
export class ChargeGroupChargeRepository extends BaseRepository<ChargeGroupCharge> {
  protected readonly modelName = 'chargeGroupCharge';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new charge group charge relationship.
   */
  async createChargeGroupCharge(
    data: Omit<ChargeGroupCharge, 'id' | 'createdAt' | 'updatedAt'>,
  ): Promise<ChargeGroupCharge> {
    return this.create(data);
  }

  /**
   * Find charge group charge by charge group ID and charge ID.
   */
  async findByChargeGroupAndChargeId(
    chargeGroupId: string,
    chargeId: string,
  ): Promise<ChargeGroupCharge | null> {
    return this.findOne({
      where: { chargeGroupId, chargeId },
    });
  }

  /**
   * Find all charges in a charge group ordered by priority.
   */
  async findChargesInGroup(chargeGroupId: string): Promise<any[]> {
    return this.findMany({
      where: { chargeGroupId },
      orderBy: { priority: 'asc' },
      include: {
        charge: {
          include: {
            percentageOfCharge: true,
          },
        },
        chargeGroup: true,
      },
    });
  }

  /**
   * Update priority of a charge group charge relationship.
   */
  async updatePriority(
    id: string,
    priority: number,
  ): Promise<ChargeGroupCharge> {
    return this.update({
      where: { id },
      data: { priority },
    });
  }

  /**
   * Delete charge group charge relationship.
   */
  async deleteByChargeGroupAndChargeId(
    chargeGroupId: string,
    chargeId: string,
  ): Promise<void> {
    console.log({
      chargeGroupId,
      chargeId,
    });
    await this.hardDelete({
      where: { chargeGroupId, chargeId },
    });
  }

  /**
   * Check if relationship exists.
   */
  async relationshipExists(
    chargeGroupId: string,
    chargeId: string,
  ): Promise<ChargeGroupCharge | null> {
    const relationship = await this.findOne({
      where: { chargeGroupId, chargeId },
    });
    return relationship;
  }

  /**
   * Get next priority for a charge group.
   */
  async getNextPriority(chargeGroupId: string): Promise<number> {
    const charges = await this.findMany({
      where: { chargeGroupId },
      orderBy: { priority: 'desc' },
      take: 1,
    });

    return charges.length > 0 ? charges[0].priority + 1 : 0;
  }
}
