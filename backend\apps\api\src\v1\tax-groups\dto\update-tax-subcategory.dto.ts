import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsNumber,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>ptional,
  IsUUID,
} from 'class-validator';

export class UpdateTaxSubcategoryDto {
  @ApiProperty({
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
    description: 'ID of existing subcategory (optional for new subcategories)',
    required: false,
  })
  @IsOptional()
  @IsUUID(4, { message: 'Subcategory ID must be a valid UUID' })
  id?: string;

  @ApiProperty({
    example: 'CGST',
    description: 'Name of the tax subcategory',
    maxLength: 100,
  })
  @IsString({ message: 'Subcategory name must be a string' })
  @IsNotEmpty({ message: 'Subcategory name is required' })
  @MaxLength(100, { message: 'Subcategory name cannot exceed 100 characters' })
  name!: string;

  @ApiProperty({
    example: 9.0,
    description: 'Percentage for this subcategory (max 2 decimal places)',
    minimum: 0.01,
    maximum: 100,
  })
  @IsNumber(
    { maxDecimalPlaces: 2 },
    { message: 'Percentage must be a number with at most 2 decimal places' },
  )
  @Min(0.01, { message: 'Percentage must be greater than 0' })
  @Max(100, { message: 'Percentage cannot exceed 100' })
  percentage!: number;
}
