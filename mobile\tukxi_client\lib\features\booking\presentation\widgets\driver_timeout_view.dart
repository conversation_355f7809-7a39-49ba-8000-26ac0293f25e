import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/constants/location_constants.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/widgets/platform_dialog.dart';
import 'package:tukxi/features/booking/presentation/widgets/nearby_driver/cancel_button.dart';

class DriverTimeoutView extends StatelessWidget {
  const DriverTimeoutView({super.key, required this.onRideCancelled});

  final VoidCallback onRideCancelled;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.only(left: 15, right: 15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(LocationConstants.mapCornerRadius),
          topRight: Radius.circular(LocationConstants.mapCornerRadius),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 1,
            offset: Offset(0, -1),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 20),
          Image.asset(
            AssetPaths.timeout,
            height: 50,
            width: 100,
            color: AppColors.black75,
          ),

          const SizedBox(height: 20),
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                'Driver Unavailable',
                textAlign: TextAlign.center,
                style: GoogleFonts.inter(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 10),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Text(
                  'No drivers available in your area right now. Please try again later.',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.black50,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 25),

          CancelRideButton(
            cancelTapped: () {
              _onCancelTapped(context);
            },
          ),
        ],
      ),
    );
  }

  void _onCancelTapped(BuildContext context) {
    PlatformDialog.showConfirmationDialog(
      context: context,
      title: 'Cancel Ride?',
      content: 'Do you want to cancel your ride request?',
      onConfirm: () {
        onRideCancelled();
      },
    );
  }
}
