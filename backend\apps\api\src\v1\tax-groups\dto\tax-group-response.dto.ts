import { ApiProperty } from '@nestjs/swagger';
import { TaxSubcategoryResponseDto } from './tax-subcategory-response.dto';

export class TaxGroupResponseDto {
  @ApiProperty({
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
    description: 'Unique identifier of the tax group',
  })
  id!: string;

  @ApiProperty({
    example: 'GST',
    description: 'Name of the tax group',
  })
  name!: string;

  @ApiProperty({
    example: 'Goods and Services Tax',
    description: 'Description of the tax group',
    nullable: true,
  })
  description?: string | null;

  @ApiProperty({
    example: 18.0,
    description: 'Total percentage (sum of all subcategories)',
  })
  totalPercentage!: number;

  @ApiProperty({
    example: true,
    description: 'Whether the tax group is active',
  })
  isActive!: boolean;

  @ApiProperty({
    example: '2025-09-26T06:57:29.000Z',
    description: 'Creation timestamp',
  })
  createdAt!: string;

  @ApiProperty({
    example: '2025-09-26T06:57:29.000Z',
    description: 'Last update timestamp',
  })
  updatedAt!: string;

  @ApiProperty({
    description: 'Tax subcategories',
    type: [TaxSubcategoryResponseDto],
  })
  subcategories!: TaxSubcategoryResponseDto[];
}
