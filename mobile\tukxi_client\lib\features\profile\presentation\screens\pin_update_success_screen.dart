import 'dart:async';

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/app_constants.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/notifiers/notifiers.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/routes/app_routes.dart';

class PinUpdateSuccessScreen extends StatefulWidget {
  const PinUpdateSuccessScreen({super.key});

  @override
  State<PinUpdateSuccessScreen> createState() => _PinUpdateSuccessScreenState();
}

class _PinUpdateSuccessScreenState extends State<PinUpdateSuccessScreen> {
  Timer? _redirectTimer;
  int _countdown = 5;

  @override
  void initState() {
    super.initState();
    _startRedirectTimer();
  }

  @override
  void dispose() {
    _redirectTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Image.asset(
                      AssetPaths.successCheck,
                      width: 108,
                      height: 108,
                      fit: BoxFit.contain,
                    ),

                    const SizedBox(height: 20),

                    Text(
                      AppConstants.pinUpdatedMessage,
                      style: GoogleFonts.inter(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                        height: 28 / 20,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 8),

                    Text(
                      AppConstants.pinUpdatedSuccessMessage,
                      style: GoogleFonts.inter(
                        fontSize: 12,
                        color: AppColors.black50,
                        fontWeight: FontWeight.w500,
                        height: 20 / 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              Text(
                'Taking you to the profile screen in $_countdown second${_countdown == 1 ? '' : 's'}...',
                style: GoogleFonts.inter(
                  fontSize: 12,
                  color: AppColors.black50,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _startRedirectTimer() {
    _redirectTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      setState(() {
        _countdown--;
      });

      if (_countdown <= 0) {
        timer.cancel();
        context.go(AppRoutes.tabbar);
        // Notify profile screen to reload if it's visible
        profileReloadNotifier.value = true;
      }
    });
  }
}
