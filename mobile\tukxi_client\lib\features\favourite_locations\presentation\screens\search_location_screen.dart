import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:go_router/go_router.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/core/widgets/title_bar.dart';
import 'package:tukxi/features/location_selection/data/models/place_suggestion.dart';
import 'package:tukxi/features/location_selection/presentation/providers/place_suggestions_provider.dart';
import 'package:tukxi/features/location_selection/presentation/widgets/location_selection/place_list_card.dart';

class SearchLocationScreen extends ConsumerStatefulWidget {
  const SearchLocationScreen({super.key, required this.currentLocation});

  final LatLng currentLocation;

  @override
  ConsumerState<SearchLocationScreen> createState() {
    return _SearchLocationScreenState();
  }
}

class _SearchLocationScreenState extends ConsumerState<SearchLocationScreen> {
  final TextEditingController _controller = TextEditingController();
  String? _searchQuery = '';
  List<PlaceSuggestion> _locations = [];
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    _controller.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _controller.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: TitleBarWithBackButton(
        title: 'Search location to add',
        titleStyle: GoogleFonts.inter(
          fontSize: 19,
          fontWeight: FontWeight.w600,
          height: 22 / 18,
        ),
        onBackPressed: () => context.pop(),
      ),
      body: SafeArea(
        top: true,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildsaerchBox(),

              const SizedBox(height: 20),

              // Use My Current Location
              InkWell(
                onTap: () => context.pop({'useCurrentLocation': true}),
                child: Row(
                  children: [
                    Image.asset(AssetPaths.destinationLocation, height: 20),
                    const SizedBox(width: 10),
                    Expanded(
                      child: Text(
                        "Use my current location",
                        style: GoogleFonts.inter(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    Icon(Icons.chevron_right),
                  ],
                ),
              ),
              if (_searchQuery != null && _searchQuery!.isNotEmpty)
                Expanded(
                  child: Column(
                    children: [
                      const SizedBox(height: 10),
                      const Divider(
                        indent: 10,
                        endIndent: 10,
                        thickness: 1.5,
                        height: 1.5,
                        color: AppColors.black10,
                      ),

                      const SizedBox(height: 20),

                      Expanded(
                        child:
                            (_locations.isEmpty &&
                                _searchQuery != null &&
                                _searchQuery!.isNotEmpty)
                            ? Center(
                                child: CircularProgressIndicator(
                                  color: AppColors.primary,
                                ),
                              )
                            : ListView.separated(
                                itemCount: _locations.length,
                                itemBuilder: (context, index) {
                                  final location = _locations[index];
                                  return InkWell(
                                    onTap: () => context.pop({
                                      'useCurrentLocation': false,
                                      'location': location,
                                    }),
                                    child: PlaceListCard(
                                      location: location,
                                      imageWidth: 42,
                                    ),
                                  );
                                },
                                separatorBuilder: (context, index) =>
                                    const SizedBox(height: 10),
                              ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildsaerchBox() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.black10, width: 1),
      ),
      child: TextField(
        controller: _controller,
        style: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.w400,
          color: AppColors.black50,
        ),
        autofocus: true,
        decoration: InputDecoration(
          border: InputBorder.none,
          isDense: true,
          prefixIcon: SizedBox(
            height: 20,
            width: 20,
            child: Center(
              child: SvgPicture.asset(
                AssetPaths.search,
                colorFilter: const ColorFilter.mode(
                  AppColors.black50,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
          contentPadding: EdgeInsets.zero,
          hintText: 'Search for a building, street name or area',
        ),
      ),
    );
  }

  void _onSearchChanged() {
    if (!mounted) return;
    if (_debounce?.isActive ?? false) _debounce!.cancel();

    _debounce = Timer(const Duration(milliseconds: 400), () async {
      if (!mounted) return;

      setState(() {
        _searchQuery = _controller.text.trim();
      });
      if (_searchQuery == null || _searchQuery!.isEmpty) {
        setState(() {
          _searchQuery = '';
          _locations = [];
        });
        return;
      }

      await _fetchPlaceSuggestions();
    });
  }

  Future _fetchPlaceSuggestions() async {
    if (!mounted) return;
    final result = await ref
        .read(placeSuggestionsProvider.notifier)
        .getPlaceSuggestions(
          query: _searchQuery!,
          location: widget.currentLocation,
        );

    result.fold(
      (failure) {
        if (!mounted) return;

        handleApiError(
          context: context,
          failure: failure,
          onRetry: () async => _fetchPlaceSuggestions(),
        );
      },
      (locationList) {
        if (!mounted) return;

        setState(() {
          _locations = locationList;
        });
      },
    );
  }
}
