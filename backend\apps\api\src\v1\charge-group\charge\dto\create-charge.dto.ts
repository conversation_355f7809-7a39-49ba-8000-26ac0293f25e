import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsNumber,
  ValidateIf,
  Min,
  Max,
  IsUUID,
} from 'class-validator';
import {
  ChargeType,
  ChargeMeter,
  PriceModel,
} from '@shared/shared/repositories/models/charge.model';

export class CreateChargeDto {
  @ApiProperty({
    description: 'Name of the charge',
    example: 'Waiting Charge',
  })
  @IsString()
  @IsNotEmpty()
  name!: string;

  @ApiPropertyOptional({
    description: 'Unique identifier for the charge',
    example: 'waiting_charge',
  })
  @IsOptional()
  @IsString()
  identifier?: string;

  @ApiProperty({
    description: 'Type of charge',
    enum: ChargeType,
    example: ChargeType.METERED,
  })
  @IsEnum(ChargeType)
  chargeType!: ChargeType;

  @ApiPropertyOptional({
    description: 'Conditional logic for when charge applies',
  })
  @IsOptional()
  condition?: any;

  @ApiPropertyOptional({
    description: 'Meter type for metered charges',
    enum: ChargeMeter,
    example: ChargeMeter.PICKUP_WAIT_DURATION,
  })
  @ValidateIf((o) => o.chargeType === ChargeType.METERED)
  @IsEnum(ChargeMeter)
  meter?: ChargeMeter;

  @ApiProperty({
    description: 'Price model for the charge',
    enum: PriceModel,
    example: PriceModel.TIERED,
  })
  @IsEnum(PriceModel)
  priceModel!: PriceModel;

  @ApiPropertyOptional({
    description: 'Price configuration based on price model',
    examples: {
      flat_amount: {
        value: {
          amount: 50,
          currency: 'USD',
        },
      },
      linear_rate: {
        value: {
          rate: 0.5,
          currency: 'USD',
        },
      },
      tiered: {
        value: {
          tiers: [
            { From: 0, To: 10, Flat_fee: '50' },
            { From: 11, To: 'inf', Rate: '100' },
          ],
        },
      },
      formula: {
        value: {
          formula: 'base_fare + (distance * rate)',
        },
      },
    },
  })
  @IsOptional()
  price!: any;

  @ApiPropertyOptional({
    description: 'Percentage for percentage-based charges (0.0001-100.0000)',
    example: 15.5,
    minimum: 0.0001,
    maximum: 100.0,
  })
  @ValidateIf((o) => o.priceModel === PriceModel.PERCENTAGE_OF_CHARGE)
  @IsNumber({ maxDecimalPlaces: 4 })
  @Min(0.0001)
  @Max(100.0)
  percentage?: number;

  @ApiPropertyOptional({
    description: 'ID of the charge to calculate percentage from',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ValidateIf((o) => o.priceModel === PriceModel.PERCENTAGE_OF_CHARGE)
  @IsUUID()
  percentageOfChargeId?: string;
}
