'use client';

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { Spinner } from '@/components/ui/spinner';
import { CityProductFare, CityProductFareStatus } from '../types/city-product-fare';

const getColumns = ({
  onEditClick,
  onDeleteClick,
  onManageChargeGroupsClick,
}: {
  onEditClick: (fare: CityProductFare) => void;
  onDeleteClick: (fare: CityProductFare) => void;
  onManageChargeGroupsClick: (fare: CityProductFare) => void;
}): ColumnDef<CityProductFare>[] => [
  {
    accessorKey: 'priority',
    header: () => (
      <div className='text-left font-semibold text-gray-600 text-sm'>
        Priority
      </div>
    ),
    cell: ({ row }) => {
      const fare = row.original;
      const isPrimaryFare = !fare.fromZoneId && !fare.toZoneId;
      return (
        <div className='text-left'>
          <div className='flex items-center gap-2'>
            <div className='text-sm font-medium text-gray-900'>{fare.priority}</div>
            {isPrimaryFare && (
              <Badge
                variant='secondary'
                className='text-xs bg-blue-100 text-blue-700'
              >
                Primary
              </Badge>
            )}
          </div>
        </div>
      );
    },
    size: 120,
  },
  {
    accessorKey: 'fromZone',
    header: () => (
      <div className='text-left font-semibold text-gray-600 text-sm'>
        From Zone
      </div>
    ),
    cell: ({ row }) => {
      const fare = row.original;
      return (
        <div className='text-left max-w-[200px]'>
          <div className='text-sm text-gray-600 truncate'>
            {fare.fromZone?.name || 'Any'}
          </div>
        </div>
      );
    },
    size: 200,
  },
  {
    accessorKey: 'toZone',
    header: () => (
      <div className='text-left font-semibold text-gray-600 text-sm'>
        To Zone
      </div>
    ),
    cell: ({ row }) => {
      const fare = row.original;
      return (
        <div className='text-left max-w-[200px]'>
          <div className='text-sm text-gray-600 truncate'>
            {fare.toZone?.name || 'Any'}
          </div>
        </div>
      );
    },
    size: 200,
  },
  {
    accessorKey: 'status',
    header: () => (
      <div className='text-left font-semibold text-gray-600 text-sm'>
        Status
      </div>
    ),
    cell: ({ row }) => {
      const fare = row.original;
      const isActive = fare.status === CityProductFareStatus.ACTIVE;
      return (
        <Badge
          variant='secondary'
          className={`text-xs ${
            isActive
              ? 'bg-green-100 text-green-700'
              : 'bg-gray-100 text-gray-700'
          }`}
        >
          {isActive ? 'Active' : 'Inactive'}
        </Badge>
      );
    },
    size: 120,
  },
  {
    accessorKey: 'fareChargeGroups',
    header: () => (
      <div className='text-left font-semibold text-gray-600 text-sm'>
        Charge Groups
      </div>
    ),
    cell: ({ row }) => {
      const fare = row.original;
      const count = fare.fareChargeGroups?.length || 0;
      return (
        <div className='text-left'>
          <div className='text-sm text-gray-600'>
            {count} {count === 1 ? 'group' : 'groups'}
          </div>
        </div>
      );
    },
    size: 150,
  },
  {
    id: 'actions',
    header: () => (
      <div className='text-center font-semibold text-gray-600 text-sm'>
        Actions
      </div>
    ),
    cell: ({ row }) => {
      const fare = row.original;
      const isPrimaryFare = !fare.fromZoneId && !fare.toZoneId;
      return (
        <div className='flex justify-center gap-1'>
          <button
            className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
            onClick={() => onEditClick(fare)}
          >
            Edit
          </button>
          <button
            className='text-sm font-medium text-purple-600 hover:text-purple-700 border border-purple-300 hover:border-purple-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
            onClick={() => onManageChargeGroupsClick(fare)}
          >
            Manage Groups
          </button>
          <button
            className={`text-sm font-medium px-3 py-1 rounded-md transition-colors bg-white border ${
              isPrimaryFare
                ? 'text-gray-400 border-gray-200 cursor-not-allowed opacity-50'
                : 'text-red-600 hover:text-red-700 border-red-300 hover:border-red-400 cursor-pointer'
            }`}
            onClick={() => !isPrimaryFare && onDeleteClick(fare)}
            disabled={isPrimaryFare}
            title={isPrimaryFare ? 'Primary fare cannot be deleted' : 'Delete fare'}
          >
            Delete
          </button>
        </div>
      );
    },
    size: 300,
  },
];

interface CityProductFareTableProps {
  data: CityProductFare[] | undefined;
  isLoading: boolean;
  onEditClick: (fare: CityProductFare) => void;
  onDeleteClick: (fare: CityProductFare) => void;
  onManageChargeGroupsClick: (fare: CityProductFare) => void;
}

export function CityProductFareTable({
  data,
  isLoading,
  onEditClick,
  onDeleteClick,
  onManageChargeGroupsClick,
}: CityProductFareTableProps) {
  const columns = getColumns({
    onEditClick,
    onDeleteClick,
    onManageChargeGroupsClick,
  });

  const table = useReactTable({
    data: data || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  if (isLoading) {
    return (
      <div className='flex items-center justify-center py-8'>
        <Spinner className='h-8 w-8' />
      </div>
    );
  }

  if (!data?.length) {
    return (
      <div className='text-center py-12 border rounded-md bg-gray-50'>
        <p className='text-gray-500 mb-4'>No fares found for this product</p>
        <p className='text-sm text-gray-400'>
          Click "Add Fare" to create your first fare rule
        </p>
      </div>
    );
  }

  return (
    <div className='space-y-2'>
      <div className='rounded-md border'>
        <div className='overflow-x-auto'>
          <table className='w-full table-fixed'>
            <thead>
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id} className='border-b bg-gray-50'>
                  {headerGroup.headers.map((header) => (
                    <th
                      key={header.id}
                      className='h-11 px-4 text-left align-middle'
                      style={{
                        width: header.getSize(),
                        maxWidth: header.getSize(),
                      }}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody>
              {table.getRowModel().rows.map((row) => (
                <tr
                  key={row.id}
                  className='border-b transition-colors hover:bg-gray-50/30'
                >
                  {row.getVisibleCells().map((cell) => (
                    <td
                      key={cell.id}
                      className='px-4 py-3 align-middle'
                      style={{
                        width: cell.column.getSize(),
                        maxWidth: cell.column.getSize(),
                      }}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
