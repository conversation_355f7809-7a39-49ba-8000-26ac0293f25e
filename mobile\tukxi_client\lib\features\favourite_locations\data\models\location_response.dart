import 'package:tukxi/features/favourite_locations/domain/entities/location_entity.dart';

class LocationResponse extends LocationEntity {
  LocationResponse({super.latitude, super.longitude, super.address});

  factory LocationResponse.fromJson(Map<String, dynamic> json) {
    return LocationResponse(
      latitude: (json['lat'] as num?)?.toDouble(),
      longitude: (json['lng'] as num?)?.toDouble(),
      address: json['address'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {'lat': latitude, 'lng': longitude, 'address': address};
  }
}

class LocationMetaResponse extends LocationMetaEntity {
  LocationMetaResponse({super.address, super.type});

  factory LocationMetaResponse.fromJson(Map<String, dynamic> json) {
    return LocationMetaResponse(
      address: json['address'] as String?,
      type: json['type'] as String?,
    );
  }
}
