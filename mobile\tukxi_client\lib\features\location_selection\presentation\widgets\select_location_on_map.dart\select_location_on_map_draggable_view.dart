import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/constants/location_constants.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/widgets/shimmer_widgets.dart';
import 'package:tukxi/features/home/<USER>/params/location_params.dart';

class SelectLocationOnMapDraggableView extends StatelessWidget {
  const SelectLocationOnMapDraggableView({
    super.key,
    required this.locationType,
    required this.isLoading,
    required this.selectedLocation,
    required this.onLocationConfirm,
  });

  final LocationParams? selectedLocation;
  final LocationType locationType;
  final bool isLoading;
  final void Function(
    LocationParams? selectedLocation,
    LocationType locationType,
  )?
  onLocationConfirm;

  @override
  Widget build(BuildContext context) {
    // return DraggableScrollableSheet(
    //   initialChildSize: UIConstants.locationSheetInitialSize,
    //   maxChildSize: UIConstants.locationSheetMaxSize,
    //   minChildSize: UIConstants.locationSheetMinSize,
    //   builder: (context, scrollController) {
    //     return NotificationListener<DraggableScrollableNotification>(
    //       onNotification: (notification) {
    //         // setState(() {
    //         //   currentExtent = notification.extent;
    //         // });
    //         return true;
    //       },
    // child:
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.only(left: 15, right: 15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(LocationConstants.mapCornerRadius),
          topRight: Radius.circular(LocationConstants.mapCornerRadius),
        ),
      ),
      child: Column(
        children: [
          // DragHandle(),
          // Expanded(
          //   child: SingleChildScrollView(
          //     controller: scrollController,
          //     child:
          Column(
            children: [
              const SizedBox(height: 15),
              _buildTopTexts(),

              const SizedBox(height: 25),
              _buildLocationText(),

              const SizedBox(height: 25),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.only(bottom: 25),
                child: FilledButton(
                  onPressed: () {
                    onLocationConfirm?.call(selectedLocation, locationType);
                  },
                  style: FilledButton.styleFrom(
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ).merge(Theme.of(context).filledButtonTheme.style),
                  child: Text(
                    locationType == LocationType.pickup
                        ? 'Select Pickup Location'
                        : 'Select Destination',
                    style: GoogleFonts.inter(
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ],
          ),
          // ),
          // ),
        ],
      ),
      //   ),
      // );
      // },
    );
  }

  ///MARK: - Widgets
  Widget _buildLocationText() {
    if (isLoading) {
      return ShimmerLoaders.textShimmer();
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Image.asset(AssetPaths.destinationLocation, height: 18, width: 18),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            selectedLocation?.locationName ?? '',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: GoogleFonts.inter(fontWeight: FontWeight.w500, fontSize: 16),
          ),
        ),
      ],
    );
  }

  Widget _buildTopTexts() {
    return Align(
      alignment: Alignment.topLeft,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            locationType == LocationType.pickup
                ? 'Set your pickup location'
                : 'Set your destination',
            style: GoogleFonts.inter(
              fontWeight: FontWeight.w600,
              fontSize: 20,
              height: 28 / 20,
            ),
          ),
          const SizedBox(height: 3),
          Text(
            'Drag map to move pin',
            style: GoogleFonts.inter(
              fontWeight: FontWeight.w400,
              fontSize: 14,
              color: AppColors.black50,
              height: 22 / 14,
            ),
          ),
        ],
      ),
    );
  }
}
