import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/theme/app_colors.dart';

class ProfileList extends StatelessWidget {
  const ProfileList({
    super.key,
    required this.title,
    required this.accountItems,
    required this.onTap,
  });

  final String title;
  final List<AccountItems> accountItems;
  final void Function(AccountItems) onTap;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 16.0),
          child: Text(
            title,
            textAlign: TextAlign.start,
            style: GoogleFonts.inter(fontSize: 16, fontWeight: FontWeight.w600),
          ),
        ),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(width: 1, color: AppColors.black10),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: accountItems.asMap().entries.map((accountEntry) {
              final idx = accountEntry.key;
              final item = accountEntry.value;
              final isLast = idx == accountItems.length - 1;

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  InkWell(
                    onTap: () => onTap(item),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              item.title,
                              style: GoogleFonts.inter(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          if (item.isDisplayArrow)
                            Icon(
                              Icons.arrow_forward_ios,
                              size: 16,
                              color: AppColors.black50,
                            ),
                        ],
                      ),
                    ),
                  ),
                  if (!isLast) Divider(color: AppColors.black10, thickness: 1),
                ],
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}
