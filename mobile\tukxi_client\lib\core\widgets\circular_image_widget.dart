import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/theme/app_colors.dart';

class CircularImageWidget extends StatelessWidget {
  final String? imageUrl;
  final double size;
  final String defaultAssetPath;
  final EdgeInsets? padding;
  final BoxBorder? border;
  final Color? backgroundColor;
  final double borderRadius;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final File? imageFile;

  const CircularImageWidget({
    super.key,
    this.imageUrl,
    this.size = 50,
    this.defaultAssetPath = AssetPaths.profPic,
    this.imageFile,
    this.padding,
    this.border,
    this.backgroundColor,
    this.borderRadius = 25,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      padding: padding,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.white,
        border: border ?? Border.all(color: Colors.white, width: 0.5),
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: _buildImage(),
      ),
    );
  }

  Widget _buildImage() {
    // Check if imageUrl is valid

    if (imageUrl != null && imageUrl!.isNotEmpty && _isValidUrl(imageUrl!)) {
      return CachedNetworkImage(
        imageUrl: imageUrl!,
        fit: fit,
        placeholder: (context, url) => placeholder ?? _buildPlaceholder(),
        errorWidget: (context, url, error) =>
            errorWidget ?? _buildDefaultImage(),
        fadeInDuration: const Duration(milliseconds: 300),
        fadeOutDuration: const Duration(milliseconds: 300),
      );
    }

    if (imageFile != null) {
      return Image.file(imageFile!, fit: BoxFit.cover);
    }

    return _buildDefaultImage();
  }

  Widget _buildDefaultImage() {
    return Image.asset(
      defaultAssetPath,
      fit: fit,
      errorBuilder: (context, error, stackTrace) => _buildFallbackIcon(),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      color: Colors.grey[300],
      child: Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            Colors.grey[600] ?? Colors.grey,
          ),
        ),
      ),
    );
  }

  Widget _buildFallbackIcon() {
    return Container(
      color: Colors.grey[300],
      child: Icon(Icons.person, size: size * 0.6, color: Colors.grey[600]),
    );
  }

  bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.isScheme('http') || uri.isScheme('https'));
    } catch (e) {
      return false;
    }
  }
}
