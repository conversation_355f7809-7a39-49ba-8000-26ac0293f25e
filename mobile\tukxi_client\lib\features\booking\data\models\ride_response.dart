import 'package:tukxi/features/booking/data/models/ride.dart';
import 'package:tukxi/features/booking/domain/entities/ride_response_entity.dart';

class RideResponse extends RideResponseEntity {
  RideResponse({
    required super.success,
    required super.message,
    required super.ride,
    required super.timestamp,
  });

  factory RideResponse.fromJson(Map<String, dynamic> json) => RideResponse(
    success: json['success'] as bool? ?? false,
    message: json['message'] as String? ?? '',
    ride: json['data'] == null ? null : Ride.fromJson(json['data']),
    timestamp: json['timestamp'] as int,
  );
}

class RideListResponse extends RideListResponseEntity {
  RideListResponse({
    required super.success,
    required super.message,
    required super.rides,
    required super.timestamp,
  });

  factory RideListResponse.fromJson(Map<String, dynamic> json) =>
      RideListResponse(
        success: json['success'] as bool? ?? false,
        message: json['message'] as String? ?? '',
        rides: json['data'] == null
            ? null
            : (json['data'] as List? ?? [])
                  .map((json) => Ride.fromJson(json))
                  .toList(),
        timestamp: json['timestamp'],
      );
}
