import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/features/location_selection/presentation/widgets/location_selection/place_list_card.dart';

class LocationSearchList extends StatelessWidget {
  const LocationSearchList({
    super.key,
    required this.locations,
    required this.setLocationOnMapTapped,
    required this.onLocationSelected,
    required this.saveAddressButtonTapped,
  });

  final void Function(dynamic location) onLocationSelected;
  final VoidCallback setLocationOnMapTapped;
  final VoidCallback saveAddressButtonTapped;
  final List<dynamic> locations;
  static const width = 42.0;

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      itemCount: locations.length + 3,
      itemBuilder: (context, index) {
        if (index == 0) {
          return const SizedBox(height: 5);
        }
        if (index > locations.length) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (index == locations.length + 1)
                _buildSetLocationOnMapButon(context),

              if (index == locations.length + 2)
                _buildSavedAddressButton(context),

              const Divider(
                indent: 10,
                endIndent: 10,
                thickness: 0.75,
                height: 1,
                color: AppColors.black10,
              ),
            ],
          );
        }

        return InkWell(
          onTap: () {
            onLocationSelected(locations[index - 1]);
          },
          child: PlaceListCard(
            location: locations[index - 1],
            imageWidth: width,
          ),
        );
      },
      separatorBuilder: (context, index) =>
          SizedBox(height: index > locations.length ? 0 : 10),
      shrinkWrap: true,
      physics: const BouncingScrollPhysics(
        parent: AlwaysScrollableScrollPhysics(),
      ),
    );
  }

  Widget _buildSetLocationOnMapButon(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: TextButton(
        onPressed: setLocationOnMapTapped,
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 10),
          foregroundColor: AppColors.primary,
          alignment: Alignment.centerLeft,
        ).merge(Theme.of(context).textButtonTheme.style),
        child: _buildImageandText(
          assetPath: AssetPaths.setOnMap,
          title: 'Set Location On Map',
        ),
      ),
    );
  }

  Widget _buildSavedAddressButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: TextButton(
        onPressed: saveAddressButtonTapped,
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 10),
          foregroundColor: AppColors.primary,
          alignment: Alignment.centerLeft,
        ).merge(Theme.of(context).textButtonTheme.style),
        child: _buildImageandText(
          assetPath: AssetPaths.viewAddress,
          title: 'View Saved Address',
        ),
      ),
    );
  }

  Widget _buildImageandText({
    required String assetPath,
    required String title,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: width,
          child: Center(child: Image.asset(assetPath, height: 18, width: 18)),
        ),
        const SizedBox(width: 10),
        Text(
          title,
          style: GoogleFonts.inter(fontSize: 12, fontWeight: FontWeight.w500),
        ),
      ],
    );
  }
}
