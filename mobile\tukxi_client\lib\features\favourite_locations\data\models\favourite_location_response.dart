import 'package:tukxi/features/favourite_locations/data/models/favourite_location.dart';
import 'package:tukxi/features/favourite_locations/domain/entities/favourite_success_response_entity.dart';

class FavoriteSuccessResponse extends FavoriteSuccessResponseEntity {
  FavoriteSuccessResponse({
    super.success,
    super.message,
    super.data,
    super.timestamp,
  });

  factory FavoriteSuccessResponse.fromJson(Map<String, dynamic> json) {
    return FavoriteSuccessResponse(
      success: json['success'] as bool?,
      message: json['message'] as String?,
      data: json['data'] != null
          ? FavouriteLocation.fromJson(json['data'] as Map<String, dynamic>)
          : null,
      timestamp: json['timestamp'] as int?,
    );
  }
}

class FavoriteSuccessListResponse extends FavoriteSuccessListResponseEntity {
  FavoriteSuccessListResponse({
    super.success,
    super.message,
    super.data,
    super.timestamp,
  });

  factory FavoriteSuccessListResponse.from<PERSON>son(Map<String, dynamic> json) {
    return FavoriteSuccessListResponse(
      success: json['success'] as bool?,
      message: json['message'] as String?,
      data: json['data'] != null
          ? FavouriteLocationListResponse.fromJson(
              json['data'] as Map<String, dynamic>,
            )
          : null,
      timestamp: json['timestamp'] as int?,
    );
  }
}
