import { IoAdapter } from '@nestjs/platform-socket.io';
import { ServerOptions } from 'socket.io';
import { createAdapter } from '@socket.io/redis-adapter';
import Redis, { RedisOptions } from 'ioredis';
import { Logger } from '@nestjs/common';
import { AppConfigService } from '@shared/shared/config';

/**
 * Custom Socket.IO adapter that configures Redis adapter for horizontal scaling
 * This approach configures the adapter at the server level instead of in the gateway
 */
export class RedisIoAdapter extends IoAdapter {
  private readonly logger = new Logger(RedisIoAdapter.name);
  private adapterConstructor: ReturnType<typeof createAdapter> | undefined;

  constructor(
    app: any,
    private readonly configService: AppConfigService,
  ) {
    super(app);
  }

  async connectToRedis(): Promise<void> {
    try {
      // Create Redis clients for the adapter using ioredis
      const redisOptions: RedisOptions = {
        host: this.configService.redisHost,
        port: this.configService.redisPort,
        db: this.configService.redisDb,
        maxRetriesPerRequest: 3,
        lazyConnect: false,
      };
      if (this.configService.redisPassword) {
        redisOptions.password = this.configService.redisPassword;
      }
      const pubClient = new Redis(redisOptions);

      const subClient = pubClient.duplicate();

      // Test Redis connection
      await pubClient.ping();
      this.logger.log('Redis connection successful');

      // Create the adapter
      this.adapterConstructor = createAdapter(pubClient, subClient);

      this.logger.log('Redis adapter configured successfully');
    } catch (error) {
      this.logger.error(
        'Failed to connect to Redis for Socket.IO adapter:',
        error,
      );
      throw error;
    }
  }

  createIOServer(port: number, options?: ServerOptions): any {
    const server = super.createIOServer(port, options);

    if (this.adapterConstructor) {
      server.adapter(this.adapterConstructor);
      this.logger.log('Redis adapter applied to Socket.IO server');
    } else {
      this.logger.warn(
        'Redis adapter not configured - using default in-memory adapter',
      );
    }

    return server;
  }
}
