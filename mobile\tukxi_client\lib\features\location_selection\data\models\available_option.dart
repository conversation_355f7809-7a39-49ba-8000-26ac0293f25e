import 'package:tukxi/features/location_selection/domain/entities/available_option_entitity.dart';

class AvailableOption extends AvailableOptionEntitity {
  AvailableOption({
    super.success,
    super.message,
    super.availableOptions,
    super.timeStamp,
  });

  factory AvailableOption.fromJson(Map<String, dynamic> json) {
    return AvailableOption(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
      availableOptions: json['data'] == null
          ? null
          : (json['data'] as List? ?? [])
                .map((json) => AvailableOptionData.fromJson(json))
                .toList(),
      timeStamp: json['timeStamp'] as int? ?? 0,
    );
  }
}

class AvailableOptionData extends AvailableOptionDataEntity {
  AvailableOptionData({
    required super.id,
    super.identifier,
    super.name,
    super.serviceName,
    super.icon,
    super.description,
    super.price,
    super.strikethroughPrice,
    super.passengerLimit,
    super.pickupToDestinationResult,
    super.driverToPickupResults,
    super.driverToDestinationResults,
  });

  factory AvailableOptionData.fromJson(Map<String, dynamic> json) {
    return AvailableOptionData(
      id: json['id'] as String? ?? '',
      identifier: json['identifier'] as String? ?? '',
      name: json['name'] as String? ?? '',
      serviceName: json['serviceName'] as String? ?? '',
      icon: json['icon'] == null ? null : json['icon'] as String? ?? '',
      description: json['description'] as String? ?? '',
      price: (json['price'] as num?)?.toDouble() ?? 0.0,
      strikethroughPrice:
          (json['strikethroughPrice'] as num?)?.toDouble() ?? 0.0,
      passengerLimit: json['passengerLimit'] as int? ?? 0,
      pickupToDestinationResult: (json['pickupToDestinationResult']) == null
          ? null
          : LocationDistanceResult.fromJson(json['pickupToDestinationResult']),
      driverToPickupResults: (json['driverToPickupResults']) == null
          ? null
          : LocationDistanceResult.fromJson(json['driverToPickupResults']),
      driverToDestinationResults: (json['driverToDestinationResults']) == null
          ? null
          : LocationDistanceResult.fromJson(json['driverToDestinationResults']),
    );
  }
}

class LocationDistanceResult extends LocationDistanceResultEntity {
  LocationDistanceResult({
    super.durationInSeconds,
    super.distanceMeters,
    super.estimatedArrivalTime,
  });

  factory LocationDistanceResult.fromJson(Map<String, dynamic> json) {
    return LocationDistanceResult(
      durationInSeconds: json['durationInSeconds'] as int? ?? 0,
      distanceMeters: (json['distanceMeters'] as num?)?.toDouble() ?? 0.0,
      estimatedArrivalTime: json['estimatedArrivalTime'] as String? ?? '',
    );
  }
}
