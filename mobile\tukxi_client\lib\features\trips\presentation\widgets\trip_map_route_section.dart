import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/constants/location_constants.dart';

class TripMapRouteSection extends ConsumerWidget {
  const TripMapRouteSection({
    super.key,
    this.myLocationEnabled = true,
    this.scrollGesturesEnabled = false,
    this.zoomGesturesEnabled = false,
    required this.markers,
    required this.pickupLocation,
    required this.destinationLocation,
    required this.polylines,
    required this.topInset,
    required this.onMapCreated,
    required this.onCameraMove,
    required this.onCameraIdle,
  });

  final bool myLocationEnabled;
  final bool scrollGesturesEnabled;
  final bool zoomGesturesEnabled;
  final double topInset;
  final Set<Marker> markers;
  final Set<Polyline> polylines;
  final LatLng? pickupLocation;
  final LatLng? destinationLocation;

  final void Function(GoogleMapController controller) onMapCreated;
  final void Function(CameraPosition position)? onCameraMove;
  final void Function()? onCameraIdle;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (pickupLocation == null ||
        destinationLocation == null ||
        pickupLocation?.latitude == 0 && pickupLocation?.longitude == 0) {
      return const Center(child: CircularProgressIndicator());
    }

    return GoogleMap(
      initialCameraPosition: CameraPosition(
        target: pickupLocation!,
        zoom: LocationConstants.zoomLevel,
      ),
      markers: markers,
      onMapCreated: onMapCreated,
      onCameraMove: onCameraMove,
      onCameraIdle: onCameraIdle,
      polylines: polylines,
      myLocationEnabled: myLocationEnabled,
      myLocationButtonEnabled: false,
      rotateGesturesEnabled: false,
      tiltGesturesEnabled: false,
      mapToolbarEnabled: false,
      zoomControlsEnabled: false,
      scrollGesturesEnabled: scrollGesturesEnabled,
      zoomGesturesEnabled: zoomGesturesEnabled,
    );
  }
}
