-- CreateEnum
CREATE TYPE "BookFor" AS ENUM ('me', 'other');

-- AlterTable
ALTER TABLE "rides" ADD COLUMN     "book_for" "BookFor" NOT NULL DEFAULT 'me',
ADD COLUMN     "created_by" UUID,
ADD COLUMN     "rider_meta" JSONB;

-- CreateIndex
CREATE INDEX "idx_ride_created_by" ON "rides"("created_by");

-- CreateIndex
CREATE INDEX "idx_ride_book_for" ON "rides"("book_for");

-- AddForeignKey
ALTER TABLE "rides" ADD CONSTRAINT "rides_created_by_fkey" FOREIGN KEY ("created_by") REFERENCES "user_profiles"("id") ON DELETE SET NULL ON UPDATE CASCADE;
