-- AlterTable
ALTER TABLE "city_product_fares" ADD COLUMN     "from_zone_type_id" UUID,
ADD COLUMN     "to_zone_type_id" UUID;

-- CreateIndex
CREATE INDEX "idx_city_product_fare_from_zone_type_id" ON "city_product_fares"("from_zone_type_id");

-- CreateIndex
CREATE INDEX "idx_city_product_fare_to_zone_type_id" ON "city_product_fares"("to_zone_type_id");

-- AddForeignKey
ALTER TABLE "city_product_fares" ADD CONSTRAINT "city_product_fares_from_zone_type_id_fkey" FOREIGN KEY ("from_zone_type_id") REFERENCES "zone_types"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeign<PERSON>ey
ALTER TABLE "city_product_fares" ADD CONSTRAINT "city_product_fares_to_zone_type_id_fkey" FOREIGN KEY ("to_zone_type_id") REFERENCES "zone_types"("id") ON DELETE SET NULL ON UPDATE CASCADE;
