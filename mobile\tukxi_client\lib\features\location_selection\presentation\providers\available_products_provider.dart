import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/features/location_selection/data/data_sources/booking_remote_data_source_Impl.dart';
import 'package:tukxi/features/location_selection/data/models/available_option.dart';
import 'package:tukxi/features/location_selection/data/repositories/booking_repository_impl.dart';
import 'package:tukxi/features/location_selection/domain/usecases/available_products_usecase.dart';
import 'package:tukxi/features/location_selection/presentation/state/available_products_state.dart';

final availableProductsProvider =
    StateNotifierProvider<AvailableProductNotifier, AvailableProductsState>((
      ref,
    ) {
      final dataSource = BookingRemoteDataSourceImpl();
      final repository = BookingRepositoryImpl(
        bookingRemoteDataSouce: dataSource,
      );
      final useCase = AvailableProductsUsecase(repository: repository);
      return AvailableProductNotifier(useCase);
    });

class AvailableProductNotifier extends StateNotifier<AvailableProductsState> {
  AvailableProductNotifier(this.usecase) : super(AvailableProductsInitial());

  final AvailableProductsUsecase usecase;

  Future<Either<Failure, AvailableOption>> fetchAvailableRideOptions({
    required LatLng pickupLocation,
    required LatLng destinationLocation,
    required RideTimeOption rideTimeOption,
    required DateTime pickupTime,
  }) async {
    state = AvailableProductsLoading();

    final result = await usecase.executefetchAvailableRideOptions(
      pickupLocation: pickupLocation,
      destinationLocation: destinationLocation,
      rideTimeOption: rideTimeOption,
      pickupTime: pickupTime,
    );

    result.fold(
      (failure) {
        if (failure.type == ErrorType.noInternet) {
          state = AvailableProductsError('no_internet');
        } else {
          state = AvailableProductsError(failure.message);
        }
      },
      (response) {
        state = AvailableProductsSuccess(availableOption: response);
      },
    );
    return result;
  }
}
