import 'package:dartz/dartz.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/features/booking/data/models/review_request.dart';
import 'package:tukxi/features/booking/data/models/review_response.dart';
import 'package:tukxi/features/booking/data/models/ride_request.dart';
import 'package:tukxi/features/booking/data/models/ride_response.dart';

abstract class RideRepository {
  Future<Either<Failure, RideResponse>> rideRequest(RideRequest request);
  Future<Either<Failure, RideResponse>> cancelRide(String rideId);
  Future<Either<Failure, RideListResponse>> fetchAllRides();
  Future<Either<Failure, RideResponse>> fetchRideDetails(String rideId);
  Future<Either<Failure, ReviewResponse>> reviewDriver(ReviewRequest request);
}
