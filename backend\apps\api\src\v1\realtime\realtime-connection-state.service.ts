import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '@shared/shared';

@Injectable()
export class RealtimeConnectionStateService {
  private readonly logger = new Logger(RealtimeConnectionStateService.name);

  private readonly KEYS = {
    CONNECTED_DRIVERS: 'realtime:drivers',
    CONNECTED_RIDERS: 'realtime:riders',
    RIDE_SUBSCRIPTIONS: 'realtime:ride_subscriptions',
    DRIVER_RIDE_MAPPING: 'realtime:driver_rides',
    USER_SOCKETS: 'realtime:user_sockets', // For multiple connections per user
  } as const;

  // TTL for connection data (in seconds) - connections expire if not refreshed
  private readonly CONNECTION_TTL = 3600; // 1 hour
  private readonly SUBSCRIPTION_TTL = 7200; // 2 hours

  constructor(private readonly redisService: RedisService) {}

  // ========================================
  // DRIVER CONNECTION MANAGEMENT
  // ========================================

  /**
   * Add a driver connection (supports multiple connections per driver)
   */
  async addDriverConnection(driverId: string, socketId: string): Promise<void> {
    try {
      const client = this.redisService.getClient();

      const driverSocketsKey = `${this.KEYS.USER_SOCKETS}:driver:${driverId}`;
      await client.sadd(driverSocketsKey, socketId);
      await client.expire(driverSocketsKey, this.CONNECTION_TTL);

      // Maintain backward compatibility - store latest socket for single connection queries
      const driverKey = `${this.KEYS.CONNECTED_DRIVERS}:${driverId}`;
      await client.setex(driverKey, this.CONNECTION_TTL, socketId);

      this.logger.debug(`Added driver connection: ${driverId} -> ${socketId}`);
    } catch (error) {
      this.logger.error(
        `Failed to add driver connection for ${driverId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Remove a specific driver connection
   */
  async removeDriverConnection(
    driverId: string,
    socketId: string,
  ): Promise<void> {
    try {
      const client = this.redisService.getClient();

      const driverSocketsKey = `${this.KEYS.USER_SOCKETS}:driver:${driverId}`;
      await client.srem(driverSocketsKey, socketId);

      const remainingConnections = await client.scard(driverSocketsKey);
      if (remainingConnections === 0) {
        await client.del(driverSocketsKey);
        await client.del(`${this.KEYS.CONNECTED_DRIVERS}:${driverId}`);

        await this.removeDriverRideMapping(driverId);
      } else {
        const remainingSockets = await client.smembers(driverSocketsKey);
        if (remainingSockets.length > 0) {
          await client.setex(
            `${this.KEYS.CONNECTED_DRIVERS}:${driverId}`,
            this.CONNECTION_TTL,
            remainingSockets[0],
          );
        }
      }

      this.logger.debug(
        `Removed driver connection: ${driverId} -> ${socketId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to remove driver connection for ${driverId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get all socket IDs for a driver
   */
  async getDriverSockets(driverId: string): Promise<string[]> {
    try {
      const client = this.redisService.getClient();
      const driverSocketsKey = `${this.KEYS.USER_SOCKETS}:driver:${driverId}`;
      return await client.smembers(driverSocketsKey);
    } catch (error) {
      this.logger.error(`Failed to get driver sockets for ${driverId}:`, error);
      return [];
    }
  }

  /**
   * Check if a driver is connected
   */
  async isDriverConnected(driverId: string): Promise<boolean> {
    try {
      const client = this.redisService.getClient();
      const driverKey = `${this.KEYS.CONNECTED_DRIVERS}:${driverId}`;
      const result = await client.exists(driverKey);
      return result === 1;
    } catch (error) {
      this.logger.error(
        `Failed to check driver connection for ${driverId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Get all connected drivers
   */
  async getConnectedDrivers(): Promise<string[]> {
    try {
      const client = this.redisService.getClient();
      const pattern = `${this.KEYS.CONNECTED_DRIVERS}:*`;
      const keys = await client.keys(pattern);
      return keys.map((key) => key.split(':').pop()!);
    } catch (error) {
      this.logger.error('Failed to get connected drivers:', error);
      return [];
    }
  }

  // ========================================
  // RIDER CONNECTION MANAGEMENT
  // ========================================

  /**
   * Add a rider connection (supports multiple connections per rider)
   */
  async addRiderConnection(riderId: string, socketId: string): Promise<void> {
    try {
      const client = this.redisService.getClient();

      // Add to rider connections set (multiple sockets per rider)
      const riderSocketsKey = `${this.KEYS.USER_SOCKETS}:rider:${riderId}`;
      await client.sadd(riderSocketsKey, socketId);
      await client.expire(riderSocketsKey, this.CONNECTION_TTL);

      // Maintain backward compatibility - store latest socket for single connection queries
      const riderKey = `${this.KEYS.CONNECTED_RIDERS}:${riderId}`;
      await client.setex(riderKey, this.CONNECTION_TTL, socketId);

      this.logger.debug(`Added rider connection: ${riderId} -> ${socketId}`);
    } catch (error) {
      this.logger.error(
        `Failed to add rider connection for ${riderId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Remove a specific rider connection
   */
  async removeRiderConnection(
    riderId: string,
    socketId: string,
  ): Promise<void> {
    try {
      const client = this.redisService.getClient();

      // Remove from rider connections set
      const riderSocketsKey = `${this.KEYS.USER_SOCKETS}:rider:${riderId}`;
      await client.srem(riderSocketsKey, socketId);

      // Check if this was the last connection
      const remainingConnections = await client.scard(riderSocketsKey);
      if (remainingConnections === 0) {
        // Remove the set and the backward compatibility key
        await client.del(riderSocketsKey);
        await client.del(`${this.KEYS.CONNECTED_RIDERS}:${riderId}`);
      } else {
        // Update the backward compatibility key with a remaining socket
        const remainingSockets = await client.smembers(riderSocketsKey);
        if (remainingSockets.length > 0) {
          await client.setex(
            `${this.KEYS.CONNECTED_RIDERS}:${riderId}`,
            this.CONNECTION_TTL,
            remainingSockets[0],
          );
        }
      }

      this.logger.debug(`Removed rider connection: ${riderId} -> ${socketId}`);
    } catch (error) {
      this.logger.error(
        `Failed to remove rider connection for ${riderId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get all socket IDs for a rider
   */
  async getRiderSockets(riderId: string): Promise<string[]> {
    try {
      const client = this.redisService.getClient();
      const riderSocketsKey = `${this.KEYS.USER_SOCKETS}:rider:${riderId}`;
      return await client.smembers(riderSocketsKey);
    } catch (error) {
      this.logger.error(`Failed to get rider sockets for ${riderId}:`, error);
      return [];
    }
  }

  /**
   * Check if a rider is connected
   */
  async isRiderConnected(riderId: string): Promise<boolean> {
    try {
      const client = this.redisService.getClient();
      const riderKey = `${this.KEYS.CONNECTED_RIDERS}:${riderId}`;
      const result = await client.exists(riderKey);
      return result === 1;
    } catch (error) {
      this.logger.error(
        `Failed to check rider connection for ${riderId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Get all connected riders
   */
  async getConnectedRiders(): Promise<string[]> {
    try {
      const client = this.redisService.getClient();
      const pattern = `${this.KEYS.CONNECTED_RIDERS}:*`;
      const keys = await client.keys(pattern);
      return keys.map((key) => key.split(':').pop()!);
    } catch (error) {
      this.logger.error('Failed to get connected riders:', error);
      return [];
    }
  }

  // ========================================
  // RIDE SUBSCRIPTION MANAGEMENT
  // ========================================

  /**
   * Add a socket to ride subscriptions
   */
  async addRideSubscription(rideId: string, socketId: string): Promise<void> {
    try {
      const client = this.redisService.getClient();
      const subscriptionKey = `${this.KEYS.RIDE_SUBSCRIPTIONS}:${rideId}`;
      await client.sadd(subscriptionKey, socketId);
      await client.expire(subscriptionKey, this.SUBSCRIPTION_TTL);

      this.logger.debug(`Added ride subscription: ${rideId} -> ${socketId}`);
    } catch (error) {
      this.logger.error(
        `Failed to add ride subscription for ${rideId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Remove a socket from ride subscriptions
   */
  async removeRideSubscription(
    rideId: string,
    socketId: string,
  ): Promise<void> {
    try {
      const client = this.redisService.getClient();
      const subscriptionKey = `${this.KEYS.RIDE_SUBSCRIPTIONS}:${rideId}`;
      await client.srem(subscriptionKey, socketId);

      // Clean up empty subscription sets
      const remainingSubscribers = await client.scard(subscriptionKey);
      if (remainingSubscribers === 0) {
        await client.del(subscriptionKey);
      }

      this.logger.debug(`Removed ride subscription: ${rideId} -> ${socketId}`);
    } catch (error) {
      this.logger.error(
        `Failed to remove ride subscription for ${rideId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get all subscribers for a ride
   */
  async getRideSubscribers(rideId: string): Promise<string[]> {
    try {
      const client = this.redisService.getClient();
      const subscriptionKey = `${this.KEYS.RIDE_SUBSCRIPTIONS}:${rideId}`;
      return await client.smembers(subscriptionKey);
    } catch (error) {
      this.logger.error(`Failed to get ride subscribers for ${rideId}:`, error);
      return [];
    }
  }

  /**
   * Remove all subscriptions for a socket (cleanup on disconnect)
   */
  async removeSocketFromAllRideSubscriptions(socketId: string): Promise<void> {
    try {
      const client = this.redisService.getClient();
      const pattern = `${this.KEYS.RIDE_SUBSCRIPTIONS}:*`;
      const keys = await client.keys(pattern);

      for (const key of keys) {
        await client.srem(key, socketId);
        // Clean up empty sets
        const remainingSubscribers = await client.scard(key);
        if (remainingSubscribers === 0) {
          await client.del(key);
        }
      }

      this.logger.debug(
        `Removed socket ${socketId} from all ride subscriptions`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to remove socket ${socketId} from ride subscriptions:`,
        error,
      );
      throw error;
    }
  }

  // ========================================
  // DRIVER-RIDE MAPPING
  // ========================================

  /**
   * Set driver's current ride
   */
  async setDriverRideMapping(driverId: string, rideId: string): Promise<void> {
    try {
      const client = this.redisService.getClient();
      const mappingKey = `${this.KEYS.DRIVER_RIDE_MAPPING}:${driverId}`;
      await client.setex(mappingKey, this.CONNECTION_TTL, rideId);

      this.logger.debug(`Set driver ride mapping: ${driverId} -> ${rideId}`);
    } catch (error) {
      this.logger.error(
        `Failed to set driver ride mapping for ${driverId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get driver's current ride
   */
  async getDriverCurrentRide(driverId: string): Promise<string | null> {
    try {
      const client = this.redisService.getClient();
      const mappingKey = `${this.KEYS.DRIVER_RIDE_MAPPING}:${driverId}`;
      return await client.get(mappingKey);
    } catch (error) {
      this.logger.error(
        `Failed to get driver current ride for ${driverId}:`,
        error,
      );
      return null;
    }
  }

  /**
   * Remove driver's ride mapping
   */
  async removeDriverRideMapping(driverId: string): Promise<void> {
    try {
      const client = this.redisService.getClient();
      const mappingKey = `${this.KEYS.DRIVER_RIDE_MAPPING}:${driverId}`;
      await client.del(mappingKey);

      this.logger.debug(`Removed driver ride mapping for ${driverId}`);
    } catch (error) {
      this.logger.error(
        `Failed to remove driver ride mapping for ${driverId}:`,
        error,
      );
      throw error;
    }
  }

  // ========================================
  // STATISTICS AND UTILITY METHODS
  // ========================================

  /**
   * Get connection statistics
   */
  async getStatistics(): Promise<{
    connectedRiders: number;
    connectedDrivers: number;
    totalConnections: number;
    activeRideSubscriptions: number;
  }> {
    try {
      const client = this.redisService.getClient();

      // Count connected drivers
      const driverPattern = `${this.KEYS.CONNECTED_DRIVERS}:*`;
      const driverKeys = await client.keys(driverPattern);
      const connectedDrivers = driverKeys.length;

      // Count connected riders
      const riderPattern = `${this.KEYS.CONNECTED_RIDERS}:*`;
      const riderKeys = await client.keys(riderPattern);
      const connectedRiders = riderKeys.length;

      // Count active ride subscriptions
      const subscriptionPattern = `${this.KEYS.RIDE_SUBSCRIPTIONS}:*`;
      const subscriptionKeys = await client.keys(subscriptionPattern);
      const activeRideSubscriptions = subscriptionKeys.length;

      return {
        connectedRiders,
        connectedDrivers,
        totalConnections: connectedRiders + connectedDrivers,
        activeRideSubscriptions,
      };
    } catch (error) {
      this.logger.error('Failed to get statistics:', error);
      return {
        connectedRiders: 0,
        connectedDrivers: 0,
        totalConnections: 0,
        activeRideSubscriptions: 0,
      };
    }
  }

  /**
   * Find user by socket ID (for cleanup operations)
   */
  async findUserBySocketId(socketId: string): Promise<{
    userId: string;
    userType: 'driver' | 'rider';
  } | null> {
    try {
      const client = this.redisService.getClient();

      // Check driver sockets
      const driverPattern = `${this.KEYS.USER_SOCKETS}:driver:*`;
      const driverKeys = await client.keys(driverPattern);

      for (const key of driverKeys) {
        const isMember = await client.sismember(key, socketId);
        if (isMember) {
          const userId = key.split(':').pop()!;
          return { userId, userType: 'driver' };
        }
      }

      // Check rider sockets
      const riderPattern = `${this.KEYS.USER_SOCKETS}:rider:*`;
      const riderKeys = await client.keys(riderPattern);

      for (const key of riderKeys) {
        const isMember = await client.sismember(key, socketId);
        if (isMember) {
          const userId = key.split(':').pop()!;
          return { userId, userType: 'rider' };
        }
      }

      return null;
    } catch (error) {
      this.logger.error(`Failed to find user by socket ID ${socketId}:`, error);
      return null;
    }
  }

  /**
   * Cleanup all data for a socket (comprehensive disconnect cleanup)
   */
  async cleanupSocketData(socketId: string): Promise<void> {
    try {
      // Find the user associated with this socket
      const userInfo = await this.findUserBySocketId(socketId);

      if (userInfo) {
        const { userId, userType } = userInfo;

        if (userType === 'driver') {
          await this.removeDriverConnection(userId, socketId);
        } else {
          await this.removeRiderConnection(userId, socketId);
        }
      }

      // Remove from all ride subscriptions
      await this.removeSocketFromAllRideSubscriptions(socketId);

      this.logger.debug(`Completed cleanup for socket ${socketId}`);
    } catch (error) {
      this.logger.error(
        `Failed to cleanup socket data for ${socketId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Health check for the service
   */
  async isHealthy(): Promise<boolean> {
    try {
      const client = this.redisService.getClient();
      await client.ping();
      return true;
    } catch (error) {
      this.logger.error('Redis health check failed:', error);
      return false;
    }
  }

  /**
   * Refresh connection TTL for a user (keep-alive mechanism)
   */
  async refreshUserConnection(
    userId: string,
    userType: 'driver' | 'rider',
  ): Promise<void> {
    try {
      const client = this.redisService.getClient();

      const userSocketsKey = `${this.KEYS.USER_SOCKETS}:${userType}:${userId}`;
      const userKey =
        userType === 'driver'
          ? `${this.KEYS.CONNECTED_DRIVERS}:${userId}`
          : `${this.KEYS.CONNECTED_RIDERS}:${userId}`;

      // Refresh TTL for both keys
      await client.expire(userSocketsKey, this.CONNECTION_TTL);
      await client.expire(userKey, this.CONNECTION_TTL);

      this.logger.debug(`Refreshed connection TTL for ${userType} ${userId}`);
    } catch (error) {
      this.logger.error(
        `Failed to refresh connection for ${userType} ${userId}:`,
        error,
      );
      throw error;
    }
  }
}
