import 'package:tukxi/features/booking/data/models/review_response.dart';
import 'package:tukxi/features/booking/data/models/ride_response.dart';

abstract class RideState {}

class RideInitial extends RideState {}

class RideLoading extends RideState {}

class RideSuccess extends RideState {
  final RideResponse? rideResponse;
  final RideListResponse? rides;
  final ReviewResponse? reviewResponse;
  final bool isSuccess;

  RideSuccess({
    this.rideResponse,
    this.rides,
    this.reviewResponse,
    this.isSuccess = false,
  });
}

class RideError extends RideState {
  final String message;
  RideError(this.message);
}
