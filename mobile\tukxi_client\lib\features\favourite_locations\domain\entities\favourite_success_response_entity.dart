import 'package:tukxi/features/favourite_locations/data/models/favourite_location.dart';

class FavoriteSuccessResponseEntity {
  const FavoriteSuccessResponseEntity({
    this.success,
    this.message,
    this.data,
    this.timestamp,
  });

  final bool? success;
  final String? message;
  final FavouriteLocation? data;
  final int? timestamp;
}

class FavoriteSuccessListResponseEntity {
  const FavoriteSuccessListResponseEntity({
    this.success,
    this.message,
    this.data,
    this.timestamp,
  });

  final bool? success;
  final String? message;
  final FavouriteLocationListResponse? data;
  final int? timestamp;
}

// {
//   "success": true,
//   "message": "Favorite locations retrieved successfully",
//   "data": {
//     "data": [
//       {
//         "id": "favorite-location-uuid-123",
//         "userProfileId": "user-profile-uuid-123",
//         "name": "Home",
//         "description": "My home address in Bangalore",
//         "location": {
//           "lat": 12.9716,
//           "lng": 77.5946
//         },
//         "meta": {
//           "address": "123 Main Street, Bangalore, Karnataka, India"
//         },
//         "userProfile": {
//           "id": "user-profile-uuid-123",
//           "firstName": "John",
//           "lastName": "Doe"
//         },
//         "createdAt": "2023-12-01T10:00:00.000Z",
//         "updatedAt": "2023-12-01T10:00:00.000Z"
//       }
//     ],
//     "total": 25,
//     "page": 1,
//     "limit": 10
//   },
//   "timestamp": 1640995200000
// }
