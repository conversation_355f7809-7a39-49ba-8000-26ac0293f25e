import 'package:tukxi/features/location_selection/data/models/available_option.dart';

class AvailableOptionEntitity {
  final bool? success;
  final String? message;
  final List<AvailableOptionData>? availableOptions;
  final int? timeStamp;

  AvailableOptionEntitity({
    required this.success,
    this.message,
    this.availableOptions,
    this.timeStamp,
  });
}

class AvailableOptionDataEntity {
  AvailableOptionDataEntity({
    required this.id,
    this.identifier,
    this.name,
    this.serviceName,
    this.icon,
    this.description,
    this.price,
    this.strikethroughPrice,
    this.passengerLimit,
    this.pickupToDestinationResult,
    this.driverToPickupResults,
    this.driverToDestinationResults,
  });
  final String id;
  final String? identifier;
  final String? name;
  final String? serviceName;
  final String? icon;
  final String? description;
  final double? price;
  final double? strikethroughPrice;
  final int? passengerLimit;
  final LocationDistanceResult? pickupToDestinationResult;
  final LocationDistanceResult? driverToPickupResults;
  final LocationDistanceResult? driverToDestinationResults;
}

class LocationDistanceResultEntity {
  const LocationDistanceResultEntity({
    this.durationInSeconds,
    this.distanceMeters,
    this.estimatedArrivalTime,
  });

  final int? durationInSeconds;
  final double? distanceMeters;
  final String? estimatedArrivalTime;
}
