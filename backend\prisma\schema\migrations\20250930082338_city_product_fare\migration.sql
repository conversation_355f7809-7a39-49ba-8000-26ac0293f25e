-- CreateTable
CREATE TABLE "city_product_fares" (
    "id" UUID NOT NULL,
    "city_product_id" UUID NOT NULL,
    "priority" INTEGER NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'active',
    "from_zone_id" UUID,
    "to_zone_id" UUID,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "city_product_fares_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "fare_charge_groups" (
    "id" UUID NOT NULL,
    "city_product_fare_id" UUID NOT NULL,
    "charge_group_id" UUID NOT NULL,
    "priority" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "fare_charge_groups_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "idx_city_product_fare_city_product_id" ON "city_product_fares"("city_product_id");

-- CreateIndex
CREATE INDEX "idx_city_product_fare_priority" ON "city_product_fares"("priority");

-- CreateIndex
CREATE INDEX "idx_city_product_fare_status" ON "city_product_fares"("status");

-- CreateIndex
CREATE INDEX "idx_city_product_fare_from_zone_id" ON "city_product_fares"("from_zone_id");

-- CreateIndex
CREATE INDEX "idx_city_product_fare_to_zone_id" ON "city_product_fares"("to_zone_id");

-- CreateIndex
CREATE INDEX "idx_fare_charge_group_city_product_fare_id" ON "fare_charge_groups"("city_product_fare_id");

-- CreateIndex
CREATE INDEX "idx_fare_charge_group_charge_group_id" ON "fare_charge_groups"("charge_group_id");

-- CreateIndex
CREATE INDEX "idx_fare_charge_group_priority" ON "fare_charge_groups"("priority");

-- CreateIndex
CREATE UNIQUE INDEX "fare_charge_groups_city_product_fare_id_charge_group_id_key" ON "fare_charge_groups"("city_product_fare_id", "charge_group_id");

-- AddForeignKey
ALTER TABLE "city_product_fares" ADD CONSTRAINT "city_product_fares_city_product_id_fkey" FOREIGN KEY ("city_product_id") REFERENCES "city_products"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "city_product_fares" ADD CONSTRAINT "city_product_fares_from_zone_id_fkey" FOREIGN KEY ("from_zone_id") REFERENCES "zones"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "city_product_fares" ADD CONSTRAINT "city_product_fares_to_zone_id_fkey" FOREIGN KEY ("to_zone_id") REFERENCES "zones"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "fare_charge_groups" ADD CONSTRAINT "fare_charge_groups_city_product_fare_id_fkey" FOREIGN KEY ("city_product_fare_id") REFERENCES "city_product_fares"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "fare_charge_groups" ADD CONSTRAINT "fare_charge_groups_charge_group_id_fkey" FOREIGN KEY ("charge_group_id") REFERENCES "charge_groups"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
