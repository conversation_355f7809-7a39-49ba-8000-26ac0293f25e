'use client';

import { CustomPagination } from '@/components/pagination';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { useState } from 'react';
import { ListRidesResponse, RideHistoryItem } from '../types/ride';
import { RideTableEmpty } from './ride-table-empty';
import { RideTableLoading } from './ride-table-loading';
import { RideDetailsSheet } from './ride-details-sheet';
import { format } from 'date-fns';

function formatStatus(status: string): string {
   return status
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
}

function getStatusBadgeClass(status: string): string {
   const normalizedStatus = status.toLowerCase();

   if (normalizedStatus.includes('completed')) {
      return 'bg-green-100 text-green-800 border-green-200';
   }
   if (normalizedStatus.includes('cancelled')) {
      return 'bg-red-100 text-red-800 border-red-200';
   }
   if (normalizedStatus.includes('progress') || normalizedStatus.includes('accepted')) {
      return 'bg-blue-100 text-blue-800 border-blue-200';
   }
   if (normalizedStatus.includes('requested') || normalizedStatus.includes('processing')) {
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
   }
   return 'bg-gray-100 text-gray-800 border-gray-200';
}

const getColumns = ({
   handleViewDetailsClick,
}: {
   handleViewDetailsClick: (id: string) => void;
}): ColumnDef<RideHistoryItem>[] => [
   {
      accessorKey: 'id',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Ride ID</div>,
      cell: ({ row }) => {
         const ride = row.original;
         return (
            <div className='text-left'>
               <div className='text-sm font-medium text-gray-900 font-mono'>
                  {ride.id.slice(0, 8)}...
               </div>
            </div>
         );
      },
      size: 110,
   },
   {
      accessorKey: 'rider',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Rider Name</div>,
      cell: ({ row }) => {
         const ride = row.original;
         return (
            <div className='text-left'>
               <div className='text-sm font-medium text-gray-900'>
                  {ride.rider.firstName} {ride.rider.lastName}
               </div>
            </div>
         );
      },
      size: 140,
   },
   {
      accessorKey: 'driver',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Driver Name</div>,
      cell: ({ row }) => {
         const ride = row.original;
         return (
            <div className='text-left'>
               {ride.driver ? (
                  <div className='text-sm font-medium text-gray-900'>
                     {ride.driver.firstName} {ride.driver.lastName}
                  </div>
               ) : (
                  <div className='text-sm text-gray-400'>Not assigned</div>
               )}
            </div>
         );
      },
      size: 140,
   },
   {
      accessorKey: 'location',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Location</div>,
      cell: ({ row }) => {
         const ride = row.original;
         const truncateAddress = (address?: string | null) => {
            if (!address) return 'N/A';
            return address.length > 20 ? address.slice(0, 20) + '...' : address;
         };
         return (
            <div className='text-left'>
               <div className='text-xs text-gray-900'>
                  <span className='font-medium'>From:</span> {truncateAddress(ride.pickupLocation.address)}
               </div>
               <div className='text-xs text-gray-600'>
                  <span className='font-medium'>To:</span> {truncateAddress(ride.destinationLocation.address)}
               </div>
            </div>
         );
      },
      size: 200,
   },
   {
      accessorKey: 'status',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Ride Status</div>,
      cell: ({ row }) => {
         const ride = row.original;
         return (
            <div className='text-left'>
               <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusBadgeClass(ride.status)}`}>
                  {formatStatus(ride.status)}
               </span>
            </div>
         );
      },
      size: 140,
   },
   {
      accessorKey: 'createdAt',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Request Time</div>
      ),
      cell: ({ row }) => {
         const ride = row.original;
         return (
            <div className='text-left'>
               <div className='text-sm font-medium text-gray-900'>
                  {format(new Date(ride.createdAt), 'MMM dd, yyyy')}
               </div>
               <div className='text-xs text-gray-500'>
                  {format(new Date(ride.createdAt), 'hh:mm a')}
               </div>
            </div>
         );
      },
      size: 140,
   },
   {
      id: 'actions',
      header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const ride = row.original;
         return (
            <div className='flex justify-center'>
               <button
                  className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
                  onClick={() => handleViewDetailsClick(ride.id)}
               >
                  View Details
               </button>
            </div>
         );
      },
      size: 120,
   },
];

interface RideTableProps {
   data: ListRidesResponse | undefined;
   isLoading: boolean;
   currentPage: number;
   onPageChange: (page: number) => void;
}

export function RideTable({ data, isLoading, currentPage, onPageChange }: RideTableProps) {
   const [rideToView, setRideToView] = useState<string | null>(null);

   const handleViewDetailsClick = (id: string) => {
      setRideToView(id);
   };

   const columns = getColumns({
      handleViewDetailsClick,
   });

   const table = useReactTable({
      data: data?.data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   if (isLoading) {
      return <RideTableLoading />;
   }

   if (!data?.data?.length) {
      return <RideTableEmpty />;
   }

   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                         header.column.columnDef.header,
                                         header.getContext()
                                      )}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td key={cell.id} className='px-4 py-3 align-middle'>
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>

         {data && data.meta && data.meta.totalPages > 1 && (
            <CustomPagination
               currentPage={currentPage}
               totalPages={data.meta.totalPages}
               onPageChange={onPageChange}
               hasNext={data.meta.hasNextPage}
               hasPrev={data.meta.hasPreviousPage}
            />
         )}

         <RideDetailsSheet
            rideId={rideToView}
            isOpen={!!rideToView}
            onClose={() => setRideToView(null)}
         />
      </div>
   );
}
