import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/extensions/string_extensions.dart';
import 'package:tukxi/core/services/rating_dialog_service.dart';
import 'package:tukxi/core/services/route_service.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/core/widgets/driver_image_with_car.dart';
import 'package:tukxi/core/widgets/shimmer_widgets.dart';
import 'package:tukxi/core/widgets/title_bar.dart';
import 'package:go_router/go_router.dart';
import 'package:tukxi/features/booking/presentation/widgets/ride_stops_card.dart';
import 'package:tukxi/features/favourite_locations/data/models/location_response.dart';
import 'package:tukxi/features/home/<USER>/params/location_params.dart';
import 'package:tukxi/features/trips/data/models/trip_history.dart';
import 'package:tukxi/features/trips/presentation/providers/trips_provider.dart';
import 'package:tukxi/features/trips/presentation/states/trip_state.dart';
import 'package:tukxi/features/trips/presentation/widgets/trip_actions_row.dart';
import 'package:tukxi/features/trips/presentation/widgets/trip_details_row.dart';
import 'package:tukxi/features/trips/presentation/widgets/trip_map_route_section.dart';
import 'package:tukxi/routes/app_routes.dart';

class TripDetailsScreen extends ConsumerStatefulWidget {
  const TripDetailsScreen({super.key, required this.trip});

  final Trip trip;

  @override
  ConsumerState<TripDetailsScreen> createState() {
    return _TripDetailsScreenState();
  }
}

class _TripDetailsScreenState extends ConsumerState<TripDetailsScreen> {
  List<LocationResponse> locations = [];
  bool isAlreadyRated = true;
  bool isFromRatingRefresh = false;
  double? _rating;
  Trip? tripDetails;
  RideStatusType _tripStatus = RideStatusType.cancelled;

  final _routeService = RouteService();

  final Set<Polyline> _dottedPolylines = {};
  Polyline? _routePolyline;
  Set<Marker> _markers = {};
  GoogleMapController? _mapController;
  List<LatLng> _coordinates = [];

  LatLng? pickupLatLng;
  LatLng? destinationLatLng;

  @override
  void initState() {
    super.initState();

    // Load initial trips
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchTripsDetails();
    });
  }

  @override
  void dispose() {
    _mapController?.dispose();
    _mapController = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final tripsState = ref.watch(tripsProvider);
    _tripStatus = tripDetails?.status ?? RideStatusType.cancelled;
    return Scaffold(
      appBar: TitleBarWithBackButton(
        title: 'Trip Details',
        titleStyle: GoogleFonts.inter(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
        onBackPressed: () => context.pop(),
      ),
      body: SafeArea(
        bottom: true,
        child: (tripsState is TripsLoading && !isFromRatingRefresh)
            ? ShimmerLoaders.tripDetailsShimmer()
            : Column(
                children: [
                  const Divider(
                    thickness: 1,
                    height: 1,
                    color: AppColors.black10,
                  ),

                  Expanded(
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Column(
                          children: [
                            const SizedBox(height: 30),
                            _buildDriverInfo(),
                            SizedBox(height: 20),

                            TripActionsRow(
                              rating: _rating,
                              status: _tripStatus,
                              onRateDriverTapped: () {
                                RatingDialogService.showRatingDialog(
                                  context: context,
                                  driverDetails: tripDetails?.driver,
                                  rideId: tripDetails?.id ?? '',
                                  isFromTripDetails: true,
                                  onReviewSubmitted: () {
                                    // Refresh trip details to show updated rating
                                    _fetchTripsDetails();
                                  },
                                );
                              },
                              onRebookTapped: () {
                                final pickupParts =
                                    (tripDetails?.pickupLocation?.address ?? '')
                                        .splitByFirstComma();
                                final destinationParts =
                                    (tripDetails
                                                ?.destinationLocation
                                                ?.address ??
                                            '')
                                        .splitByFirstComma();
                                context.push(
                                  AppRoutes.availableProducts,
                                  extra: {
                                    'pickupLocation': LocationParams(
                                      latLng: pickupLatLng,
                                      locationName: pickupParts.first,
                                      locationAddress: pickupParts.last,
                                    ),
                                    'destinationLocation': LocationParams(
                                      latLng: destinationLatLng,
                                      locationName: destinationParts.first,
                                      locationAddress: destinationParts.last,
                                    ),
                                    'rideTimeOption': RideTimeOption.now,
                                  },
                                );
                              },
                            ),
                            SizedBox(height: 20),

                            _buildMapSection(),
                            SizedBox(height: 20),

                            _buildTripItinerary(),
                            SizedBox(height: 20),

                            _buildAdditionalDetails(),
                            SizedBox(height: 20),
                          ],
                        ),
                      ),
                    ),
                  ),

                  SizedBox(height: 10),

                  _buildCustomerSupport(),

                  if (Platform.isAndroid) SizedBox(height: 15),
                ],
              ),
      ),
    );
  }

  Widget _buildMapSection() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: AspectRatio(
        aspectRatio: 16 / 9,
        child: TripMapRouteSection(
          myLocationEnabled: false,
          scrollGesturesEnabled: false,
          zoomGesturesEnabled: false,
          markers: {..._markers},
          pickupLocation: pickupLatLng,
          destinationLocation: destinationLatLng,
          polylines: {
            if (_routePolyline != null) _routePolyline!,
            ..._dottedPolylines,
          },
          topInset: 0,
          onMapCreated: _onMapCreated,
          onCameraMove: (position) {},
          onCameraIdle: () {},
        ),
      ),
    );
  }

  Widget _buildCustomerSupport() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.greyBg,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 2,
            offset: Offset(1, 1),
            spreadRadius: 1,
          ),
        ],
      ),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      child: InkWell(
        onTap: () {},
        child: Row(
          children: [
            Image.asset(AssetPaths.support, height: 24, width: 24),
            SizedBox(width: 10),
            Expanded(child: Text('Customer Support')),
            SizedBox(width: 10),

            Image.asset(AssetPaths.arrowNext, height: 24, width: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Additional details',
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: AppColors.black10, width: 1),
            borderRadius: BorderRadius.circular(16),
          ),
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              TripDetailsRow(
                label: 'Trip Status',
                value: _tripStatus.name.toCapitalize(),
                color:
                    (_tripStatus == RideStatusType.completed ||
                        _tripStatus == RideStatusType.cancelled)
                    ? _tripStatus.color
                    : Colors.black,
              ),
              if (_tripStatus == RideStatusType.completed)
                Column(
                  children: [
                    _buildSeparator(),
                    TripDetailsRow(label: 'Total fare', value: '₹ 0.00'),

                    _buildSeparator(),
                    TripDetailsRow(label: 'Payment method', value: 'Cash'),

                    _buildSeparator(),
                    TripDetailsRow(label: 'Trip duration', value: '0 mins'),
                  ],
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSeparator() {
    return Divider(thickness: 1, height: 25, color: AppColors.black10);
  }

  Widget _buildDriverInfo() {
    return Container(
      alignment: Alignment.center,
      child: Column(
        children: [
          DriverImageWithCar(
            carImageUrl: tripDetails?.product?.icon ?? '',
            imageUrl: tripDetails?.driver?.profilePic ?? '',
          ),
          // Driver name
          Text(
            driverName(),
            style: GoogleFonts.inter(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          SizedBox(height: 4),
          // Car details
          Text(
            vehicleDetails(),
            style: GoogleFonts.inter(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: AppColors.black50,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTripItinerary() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Trip Itinerary',
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 16),

        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: AppColors.black10, width: 1),
            borderRadius: BorderRadius.circular(16),
          ),
          padding: const EdgeInsets.all(16),
          child: ListView.builder(
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemCount: locations.length,
            itemBuilder: (context, index) {
              final isNotLastIndex = index != locations.length - 1;
              final isFirstIndex = index == 0;
              final isLastIndex = index == locations.length - 1;
              return RideStopsCard(
                selectedIndex: -1,
                index: index,
                stop: locations[index].address,
                isPickup: isFirstIndex,
                isDestination: isLastIndex,
                isNotLastIndex: isNotLastIndex,
                isFromTripDetails: true,
              );
            },
          ),
        ),
      ],
    );
  }

  //MARK: - Map Related Methods

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;

    _drawRoute();
  }

  Future<void> _drawRoute() async {
    if (!mounted ||
        _mapController == null ||
        pickupLatLng == null ||
        destinationLatLng == null) {
      return;
    }

    //Set initial markers immediately
    _setMarkers(pickup: pickupLatLng!, destination: destinationLatLng!);

    //Snap pickup and destination to nearest roads
    final snappedPickup = await _routeService.snapToRoad(pickupLatLng!);

    final snappedDestination = await _routeService.snapToRoad(
      destinationLatLng!,
    );
    final routeInfo = await _routeService.getRoute(
      origin: snappedPickup,
      destination: snappedDestination,
    );

    if (!mounted || routeInfo == null) return;

    _updateRouteDisplay(routeInfo, snappedPickup, snappedDestination);
  }

  void _updateRouteDisplay(
    dynamic routeInfo,
    LatLng snappedPickup,
    LatLng snappedDestination,
  ) {
    _coordinates = routeInfo.polylineCoordinates;

    if (!_coordinates.contains(snappedPickup)) {
      _coordinates.insert(0, snappedPickup);
    }
    if (!_coordinates.contains(snappedDestination)) {
      _coordinates.add(snappedDestination);
    }

    // Draw base route (static)
    setState(() {
      _updatePolylines(snappedPickup, snappedDestination);
    });
    final bounds = routeInfo.bounds;

    _mapController!.animateCamera(CameraUpdate.newLatLngBounds(bounds, 50));
  }

  void _updatePolylines(LatLng snappedPickup, LatLng snappedDestination) {
    if (pickupLatLng != snappedPickup) {
      _dottedPolylines.addAll(
        createDottedPolyline(
          start: pickupLatLng!,
          end: snappedPickup,
          idPrefix: 'pickup-offroad',
          color: Colors.purple,
        ),
      );
    }

    _routePolyline = Polyline(
      polylineId: const PolylineId('route'),
      points: _coordinates,
      color: const Color.fromARGB(255, 6, 49, 240),
      width: 5,
      startCap: Cap.roundCap,
      endCap: Cap.roundCap,
      jointType: JointType.round,
    );

    if (destinationLatLng != snappedDestination) {
      _dottedPolylines.addAll(
        createDottedPolyline(
          start: snappedDestination,
          end: destinationLatLng!,
          idPrefix: 'destination-offroad',
          color: Colors.purple,
        ),
      );
    }
  }

  /// Creates a dotted polyline between two points.
  /// Works on both Android & iOS (no reliance on patterns).
  Set<Polyline> createDottedPolyline({
    required LatLng start,
    required LatLng end,
    String idPrefix = 'dot',
    Color color = const Color(0xFF6A1B9A),
    double width = 6,
    int segments = 40,
  }) {
    final List<LatLng> points = [];

    // Divide the line into segments
    for (int i = 0; i <= segments; i++) {
      final lat =
          start.latitude + (end.latitude - start.latitude) * (i / segments);
      final lng =
          start.longitude + (end.longitude - start.longitude) * (i / segments);
      points.add(LatLng(lat, lng));
    }

    final Set<Polyline> polylines = {};
    // Draw shorter segments with larger gaps for rounder dots
    for (int i = 0; i < points.length - 1; i += 4) {
      // Increased gap
      if (i + 1 >= points.length) break;
      polylines.add(
        Polyline(
          polylineId: PolylineId('$idPrefix-$i'),
          points: [points[i], points[i + 1]],
          color: AppColors.black50, // Added opacity
          width: width.toInt(),
        ),
      );
    }

    return polylines;
  }

  void _setMarkers({
    required LatLng pickup,
    required LatLng destination,
  }) async {
    setState(() {
      _markers = {
        Marker(
          markerId: const MarkerId('pickup'),
          position: pickup,
          icon: BitmapDescriptor.defaultMarkerWithHue(
            BitmapDescriptor.hueGreen,
          ),
        ),
        Marker(
          markerId: const MarkerId('destination'),
          position: destination,
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
        ),
      };
    });
  }

  //MARK: - API Calls
  Future<void> _fetchTripsDetails({bool isRefresh = false}) async {
    isFromRatingRefresh = isRefresh;
    final result = await ref
        .read(tripsProvider.notifier)
        .fetchTripDetails(tripId: widget.trip.id);

    result.fold(
      (failure) {
        if (!mounted) return;

        handleApiError(
          context: context,
          failure: failure,
          onRetry: () => _fetchTripsDetails(isRefresh: isRefresh),
        );
      },
      (tripResponse) {
        setState(() {
          tripDetails = tripResponse.trip;
          if (tripDetails == null) return;

          locations.clear();

          pickupLatLng = LatLng(
            tripDetails!.pickupLocation?.latitude ?? 0,
            tripDetails!.pickupLocation?.longitude ?? 0,
          );
          destinationLatLng = LatLng(
            tripDetails!.destinationLocation?.latitude ?? 0,
            tripDetails!.destinationLocation?.longitude ?? 0,
          );

          if (tripDetails?.pickupLocation != null) {
            locations.add(tripDetails!.pickupLocation!);
          }

          if (tripDetails?.stops != null) {
            locations.addAll(tripDetails!.stops!);
          }

          if (tripDetails?.destinationLocation != null) {
            locations.add(tripDetails!.destinationLocation!);
          }
          _rating = double.tryParse(tripDetails?.rating?.rating ?? '0');

          isFromRatingRefresh = false;
        });
      },
    );
  }

  String vehicleDetails() {
    if (tripDetails != null && tripDetails?.driverVehicle != null) {
      final vehicleType = tripDetails?.driverVehicle?.vehicleType?.name ?? '';
      final vehicleRegNumber = tripDetails?.driverVehicle?.vehicleNumber ?? '';

      return '$vehicleType • $vehicleRegNumber';
    } else {
      return 'Unknown';
    }
  }

  String driverName() {
    if (tripDetails != null && tripDetails?.driver != null) {
      return '${tripDetails?.driver?.firstName ?? ''} ${tripDetails?.driver?.lastName ?? ''}';
    } else {
      return 'Unknown';
    }
  }

  // void _refreshMap() {
  //   setState(() {
  //     _mapKey = UniqueKey();
  //     _mapController = null;
  //     _markers.clear();
  //     _dottedPolylines.clear();
  //     _routePolyline = null;
  //   });
  // }
}
