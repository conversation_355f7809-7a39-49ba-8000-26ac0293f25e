import 'dart:async';
import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/constants/location_constants.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/core/extensions/latlng_extensions.dart';
import 'package:tukxi/core/services/google_place_service.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/core/utils/snack_bar_utils.dart';
import 'package:tukxi/core/widgets/title_bar.dart';
import 'package:tukxi/features/location_selection/data/models/place_suggestion.dart';
import 'package:tukxi/features/location_selection/presentation/providers/place_suggestions_provider.dart';
import 'package:tukxi/features/favourite_locations/data/models/favourite_location.dart';
import 'package:tukxi/features/favourite_locations/presentation/widgets/add_address_confirm_sheet.dart';
import 'package:tukxi/features/favourite_locations/presentation/widgets/add_favourite_draggable_view.dart';
import 'package:tukxi/features/favourite_locations/presentation/widgets/favourite_map_section.dart';
import 'package:tukxi/features/home/<USER>/params/location_params.dart';
import 'package:tukxi/features/home/<USER>/providers/location_provider.dart';
import 'package:tukxi/routes/app_routes.dart';

class AddFavouriteLocationScreen extends ConsumerStatefulWidget {
  const AddFavouriteLocationScreen({
    super.key,
    required this.favouriteLocation,
  });

  final FavouriteLocation? favouriteLocation;

  @override
  ConsumerState<AddFavouriteLocationScreen> createState() {
    return _AddFavouriteLocationScreenState();
  }
}

class _AddFavouriteLocationScreenState
    extends ConsumerState<AddFavouriteLocationScreen> {
  static const double _minDistance = 10;

  final _bottomViewKey = GlobalKey();

  DateTime? _lastUpdate;
  double _mapAreaHeight = 0;
  GoogleMapController? _mapController;
  LatLng? _currentLocation;
  LocationParams? _currentPlaceDetails;
  LocationParams? _placeDetails;
  bool _isUserLocationEnabled = false;
  LatLng? _pickedlocation;

  Timer? _debounce;
  LatLng? _lastFetched;
  bool _isLoading = true;
  bool haveFaveLocation() {
    return widget.favouriteLocation != null &&
        widget.favouriteLocation!.location != null &&
        widget.favouriteLocation!.location!.latitude != null &&
        widget.favouriteLocation!.location!.longitude != null;
  }

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_currentLocation != null && widget.favouriteLocation == null) {
        _fetchPlaceDetails(coordinates: _currentLocation);
      }
    });
  }

  @override
  void dispose() {
    _debounce?.cancel();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final locationState = ref.watch(locationProvider);
    _isUserLocationEnabled =
        locationState.gpsEnabled && locationState.permissionGranted;
    _currentLocation = _isUserLocationEnabled
        ? locationState.gpsLocation
        : locationState.ipLocation;

    return Scaffold(
      appBar: TitleBarWithBackButton(
        title: 'Select pick-up location',
        titleStyle: GoogleFonts.inter(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          height: 28 / 20,
        ),
        onBackPressed: () => context.pop(),
      ),
      body: SafeArea(
        top: true,
        bottom: Platform.isAndroid,
        child: LayoutBuilder(
          builder: (context, constraints) {
            _mapAreaHeight = constraints.maxHeight;
            return Stack(
              children: [
                Positioned.fill(
                  child: FavouriteMapSection(
                    mapHeight: _mapAreaHeight,
                    currentLocation: _currentLocation,
                    isUserLocationEnabled: _isUserLocationEnabled,
                    hasOverlayElements: false,
                    onCameraIdle: () {
                      LatLng? favLatLng;
                      if (haveFaveLocation()) {
                        favLatLng = LatLng(
                          widget.favouriteLocation!.location!.latitude!,
                          widget.favouriteLocation!.location!.longitude!,
                        );
                        if (!favLatLng.isSameLocation(_pickedlocation!)) {
                          _onCameraIdleDebounced(_pickedlocation!);
                        }
                      }

                      if (_pickedlocation != null &&
                          widget.favouriteLocation == null) {
                        _onCameraIdleDebounced(_pickedlocation!);
                      }
                    },
                    onCameraMove: (position) {
                      setState(() {
                        _isLoading = true;
                      });
                      LatLng? favLatLng;
                      if (haveFaveLocation()) {
                        favLatLng = LatLng(
                          widget.favouriteLocation!.location!.latitude!,
                          widget.favouriteLocation!.location!.longitude!,
                        );

                        if (!favLatLng.isSameLocation(position.target)) {
                          _pickedlocation = position.target;
                        }
                      } else if (widget.favouriteLocation == null) {
                        _pickedlocation = position.target;
                      }
                    },
                    searchTapped: _onSearchTapped,
                    onMapCreated: _onMapCreated,
                  ),
                ),
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: AddFavouriteDraggableView(
                    key: _bottomViewKey,
                    placedetails: _placeDetails,
                    isLoading: _isLoading,
                    onConfirmTapped: _onConfirmTapped,
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  void _updateMapPadding() {
    if (!mounted || _mapAreaHeight <= 0) return;

    final RenderBox box =
        _bottomViewKey.currentContext!.findRenderObject() as RenderBox;

    final newPadding = box.size.height + 5;

    // ✅ Throttle (only update if >100ms since last update OR big change)
    final now = DateTime.now();
    if (_lastUpdate == null ||
        now.difference(_lastUpdate!) > const Duration(milliseconds: 100) ||
        (ref.read(mapPaddingProvider) - newPadding).abs() > 5) {
      ref.read(mapPaddingProvider.notifier).state = newPadding;

      _lastUpdate = now;
    }
    setState(() {});
  }

  Future _fetchPlaceDetails({required LatLng? coordinates}) async {
    if (!mounted) return;
    await _handleApiCall(
      () => ref
          .read(placeSuggestionsProvider.notifier)
          .getPlaceDetailsFromCoordinates(coordinates),
      onSuccess: (response) {
        if (!mounted) return;
        setState(() {
          _currentPlaceDetails = response;
          _placeDetails = response;
          _lastFetched = coordinates;
          _isLoading = false;
        });
      },
    );
  }

  Future _fetchCoordinatesAsynchronously({
    required PlaceSuggestion location,
  }) async {
    await _handleApiCall(
      () => ref
          .read(placeSuggestionsProvider.notifier)
          .getCoordinatesFromPlaceID(location.placeId),
      onSuccess: (coordinates) {
        if (!mounted || coordinates == null) return;

        final placeWithLatLng = LocationParams(
          id: location.placeId,
          locationName: location.name,
          latLng: coordinates,
          locationAddress: location.address,
        );
        setState(() {
          _placeDetails = placeWithLatLng;
        });
        _mapController?.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(
              target: coordinates,
              zoom: LocationConstants.zoomLevel,
            ),
          ),
        );
      },
    );
  }

  Future<void> _handleApiCall<T>(
    Future<Either<Failure, T>> Function() apiCall, {
    Function(T response)? onSuccess,
    String? errorMessage,
  }) async {
    final result = await apiCall();
    if (!mounted) return;

    result.fold(
      (failure) {
        if (!mounted) return;
        handleApiError(
          context: context,
          failure: failure,
          errorMessage: errorMessage,
          onRetry: () async => _handleApiCall(apiCall, onSuccess: onSuccess),
        );
      },
      (response) {
        if (!mounted) return;
        onSuccess?.call(response);
      },
    );
  }

  /// Debounced place fetch
  void _onCameraIdleDebounced(LatLng location) {
    if (_pickedlocation == null) return;

    if (_lastFetched != null) {
      final dist = GooglePlacesService.calculateDistance(
        _lastFetched!.latitude,
        _lastFetched!.longitude,
        location.latitude,
        location.longitude,
      );
      if (dist < _minDistance) return;
    }
    _updateMapPadding();

    _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 400), () {
      if (!mounted) return;
      _fetchPlaceDetails(coordinates: location);
    });
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;

    _updateMapPadding();

    // if (_currentLocation != null) {
    //   controller.animateCamera(
    //     CameraUpdate.newCameraPosition(
    //       CameraPosition(
    //         target: _currentLocation!,
    //         zoom: LocationConstants.zoomLevel,
    //       ),
    //     ),
    //   );
    // }
  }

  void _onSearchTapped() async {
    final result = await context.push<Map<String, dynamic>>(
      AppRoutes.searchLocation,
      extra: {'currentLocation': _currentLocation},
    );

    if (result != null) {
      final useCurrentLocation = result['useCurrentLocation'] as bool;

      final place = result['location'] as PlaceSuggestion?;

      if (useCurrentLocation) {
        setState(() {
          _placeDetails = _currentPlaceDetails;
        });
        if (_currentPlaceDetails != null) {
          _mapController?.animateCamera(
            CameraUpdate.newCameraPosition(
              CameraPosition(
                target: _currentPlaceDetails!.latLng!,
                zoom: LocationConstants.zoomLevel,
              ),
            ),
          );
        }
      } else if (place != null) {
        _fetchCoordinatesAsynchronously(location: place);
      }
    }
  }

  void _onConfirmTapped() {
    if (_placeDetails != null) {
      showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        isScrollControlled: true,
        builder: (ctx) {
          return AddAddressConfirmSheet(
            placeDetails: _placeDetails,
            scaffoldContext: ctx,
            favouriteLocation: widget.favouriteLocation,
          );
        },
      );
    } else {
      SnackbarUtils.showSnackBar(
        context: context,
        message: 'Please select a place',
        type: SnackBarType.info,
      );
      return;
    }
  }
}
