'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { MapPin, Edit, Trash2 } from 'lucide-react';
import { Zone } from '../types/city-zone';
import { CityZoneTableEmpty } from './city-zone-table-empty';
import { CityZoneTableLoading } from './city-zone-table-loading';

interface CityZoneTableData {
   data: Zone[];
   meta?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
   };
}

interface CityZoneTableProps {
   data?: CityZoneTableData;
   isLoading: boolean;
   hasFilters: boolean;
   onEditClick: (zone: Zone) => void;
   onDeleteClick: (zone: Zone) => void;
   onMapClick: (zone: Zone) => void;
}

const getColumns = ({
   onEditClick,
   onDeleteClick,
   onMapClick,
}: {
   onEditClick: (zone: Zone) => void;
   onDeleteClick: (zone: Zone) => void;
   onMapClick: (zone: Zone) => void;
}): ColumnDef<Zone>[] => [
   {
      accessorKey: 'name',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Zone Name</div>,
      cell: ({ row }) => {
         const zone = row.original as Zone;
         return (
            <div className='min-w-0'>
               <div className='font-medium text-sm text-gray-900 truncate'>
                  {zone.name}
               </div>
               {zone.description && (
                  <div className='text-xs text-gray-500 truncate'>
                     {zone.description}
                  </div>
               )}
            </div>
         );
      },
      size: 200,
   },
   {
      accessorKey: 'zoneType',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Zone Type</div>
      ),
      cell: ({ row }) => {
         const zone = row.original as Zone;
         const getZoneTypeColor = (zoneType?: { algorithm?: string }) => {
            if (!zoneType?.algorithm) return 'bg-gray-100 text-gray-700';
            
            switch (zoneType.algorithm.toLowerCase()) {
               case 'city':
                  return 'bg-blue-100 text-blue-700';
               case 'airport':
                  return 'bg-purple-100 text-purple-700';
               case 'highway':
                  return 'bg-green-100 text-green-700';
               case 'suburban':
                  return 'bg-yellow-100 text-yellow-700';
               case 'rural':
                  return 'bg-orange-100 text-orange-700';
               default:
                  return 'bg-gray-100 text-gray-700';
            }
         };

         return (
            <div className='text-left'>
               <Badge
                  variant='secondary'
                  className={`text-xs ${getZoneTypeColor(zone.zoneType)}`}
               >
                  {zone.zoneType?.name || 'Unknown'}
               </Badge>
               {zone.zoneType?.algorithm && (
                  <div className='text-xs text-gray-500 mt-1'>
                     {zone.zoneType.algorithm}
                  </div>
               )}
            </div>
         );
      },
      size: 150,
   },
   {
      accessorKey: 'createdAt',
      header: () => (
         <div className='text-left font-semibold text-gray-600 text-sm'>Created</div>
      ),
      cell: ({ row }) => {
         const zone = row.original as Zone;
         const formatDate = (dateString: string) => {
            return new Date(dateString).toLocaleDateString('en-US', {
               year: 'numeric',
               month: 'short',
               day: 'numeric',
            });
         };

         return (
            <div className='text-left'>
               <div className='text-sm text-gray-600'>{formatDate(zone.createdAt)}</div>
            </div>
         );
      },
      size: 120,
   },
   {
      accessorKey: 'actions',
      header: () => <div className='text-right font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const zone = row.original as Zone;
         return (
            <div className='flex items-center justify-end gap-2'>
               <Button
                  variant='outline'
                  size='sm'
                  onClick={() => onMapClick(zone)}
                  className='flex items-center gap-1 text-blue-600 hover:text-blue-700'
                  title='Open Map'
               >
                  <MapPin className='w-3 h-3' />
                  Map
               </Button>
               
               <Button
                  variant='outline'
                  size='sm'
                  onClick={() => onEditClick(zone)}
                  className='flex items-center gap-1 text-gray-600 hover:text-gray-700'
               >
                  <Edit className='w-3 h-3' />
                  Edit
               </Button>

               <Button
                  variant='outline'
                  size='sm'
                  onClick={() => onDeleteClick(zone)}
                  className='text-red-600 hover:text-red-700'
               >
                  <Trash2 className='w-3 h-3' />
               </Button>
            </div>
         );
      },
      size: 200,
   },
];

export function CityZoneTable({
   data,
   isLoading,
   hasFilters,
   onEditClick,
   onDeleteClick,
   onMapClick,
}: CityZoneTableProps) {
   const columns = getColumns({
      onEditClick,
      onDeleteClick,
      onMapClick,
   });

   const table = useReactTable({
      data: data?.data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   if (isLoading) {
      return <CityZoneTableLoading />;
   }

   if (!data?.data?.length) {
      return <CityZoneTableEmpty hasFilters={hasFilters} />;
   }

   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                         header.column.columnDef.header,
                                         header.getContext()
                                      )}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td key={cell.id} className='px-4 py-3 align-middle'>
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>
      </div>
   );
}