import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsInt,
  Min,
  Max,
  IsString,
  IsEnum,
  IsNumber,
  IsDateString,
} from 'class-validator';
import { Type } from 'class-transformer';
import {
  ChargeType,
  PriceModel,
} from '@shared/shared/repositories/models/charge.model';

export class ChargePaginationDto {
  @ApiProperty({
    description: 'Page number',
    example: 1,
    minimum: 1,
    required: false,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    minimum: 1,
    maximum: 100,
    required: false,
    default: 10,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Search term for charge name or identifier',
    example: 'waiting',
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: 'Filter by charge type',
    enum: ChargeType,
    example: ChargeType.METERED,
  })
  @IsOptional()
  @IsEnum(ChargeType)
  chargeType?: ChargeType;

  @ApiPropertyOptional({
    description: 'Filter by price model',
    enum: PriceModel,
    example: PriceModel.TIERED,
  })
  @IsOptional()
  @IsEnum(PriceModel)
  priceModel?: PriceModel;

  @ApiPropertyOptional({
    description:
      'Minimum amount filter (for flat amount and linear rate price models)',
    example: 10.0,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  minAmount?: number;

  @ApiPropertyOptional({
    description:
      'Maximum amount filter (for flat amount and linear rate price models)',
    example: 100.0,
    minimum: 0,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(0)
  maxAmount?: number;

  @ApiPropertyOptional({
    description: 'Filter by creation date from (ISO 8601 format)',
    example: '2023-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsDateString()
  createdFrom?: string;

  @ApiPropertyOptional({
    description: 'Filter by creation date to (ISO 8601 format)',
    example: '2023-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @IsDateString()
  createdTo?: string;

  @ApiPropertyOptional({
    description: 'Filter by charge group ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @IsOptional()
  @IsString()
  chargeGroupId?: string;

  @ApiPropertyOptional({
    description: 'Include charges that are not attached to any charge group',
    example: true,
    default: false,
  })
  @IsOptional()
  @Type(() => Boolean)
  includeUnattached?: boolean = false;
}
