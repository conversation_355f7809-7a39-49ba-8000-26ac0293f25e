import 'dart:io';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/location_constants.dart';
import 'package:tukxi/core/widgets/drag_handle.dart';
import 'package:tukxi/features/booking/presentation/widgets/confirm_pickup/zone_card.dart';

class ConfirmPickpDraggableView extends StatelessWidget {
  const ConfirmPickpDraggableView({
    super.key,
    required this.scrollController,
    required this.zones,
  });

  final ScrollController scrollController;
  final List<dynamic> zones;
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.only(left: 15, right: 15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(LocationConstants.mapCornerRadius),
          topRight: Radius.circular(LocationConstants.mapCornerRadius),
        ),
      ),
      child: CustomScrollView(
        controller: scrollController,
        physics: Platform.isAndroid
            ? const ClampingScrollPhysics()
            : const BouncingScrollPhysics(parent: ClampingScrollPhysics()),
        slivers: [
          SliverToBoxAdapter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                DragHandle(),
                const SizedBox(height: 30),
                Text(
                  'Confirm pick-up location',
                  style: GoogleFonts.inter(
                    fontWeight: FontWeight.w600,
                    fontSize: 20,
                    height: 28 / 20,
                  ),
                ),

                const SizedBox(height: 25),
              ],
            ),
          ),
          SliverList(
            delegate: SliverChildBuilderDelegate((context, index) {
              return ZoneCard();
            }, childCount: 5),
          ),
        ],
      ),
    );
  }
}
