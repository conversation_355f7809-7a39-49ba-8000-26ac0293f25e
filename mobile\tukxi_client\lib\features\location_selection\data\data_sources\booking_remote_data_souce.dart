import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/features/location_selection/data/models/available_option.dart';

abstract class BookingRemoteDataSouce {
  Future<AvailableOption> fetchAvailableRideOptions({
    required LatLng pickupLocation,
    required LatLng destinationLocation,
    required RideTimeOption rideTimeOption,
    required DateTime pickupTime,
  });
}
