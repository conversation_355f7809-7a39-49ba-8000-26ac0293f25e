#!/usr/bin/env node

/**
 * Test script to verify Redis adapter configuration works
 * This simulates the NestJS WebSocket gateway initialization
 */

const { Server } = require('socket.io');
const { createServer } = require('http');
const { createAdapter } = require('@socket.io/redis-adapter');
const Redis = require('ioredis');

async function testRedisAdapter() {
  console.log('🧪 Testing Redis adapter configuration...');
  
  try {
    // Create HTTP server
    const httpServer = createServer();
    
    // Create Socket.IO server
    const io = new Server(httpServer, {
      cors: { origin: '*' }
    });
    
    console.log('✅ Socket.IO server created');
    
    // Create Redis clients (similar to what the gateway does)
    const pubClient = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379,
      password: process.env.REDIS_PASSWORD || '',
      db: process.env.REDIS_DB || 0,
      maxRetriesPerRequest: 3,
      lazyConnect: false,
    });
    
    const subClient = pubClient.duplicate();
    
    console.log('✅ Redis clients created');
    
    // Test Redis connection
    await pubClient.ping();
    console.log('✅ Redis connection successful');
    
    // Configure Redis adapter (this is what was failing)
    io.adapter(createAdapter(pubClient, subClient));
    console.log('✅ Redis adapter configured successfully');
    
    // Test that adapter method exists and works
    if (typeof io.adapter === 'function') {
      console.log('✅ io.adapter is a function');
    } else {
      console.log('❌ io.adapter is not a function');
    }
    
    // Start server on a test port
    const port = 3333;
    httpServer.listen(port, () => {
      console.log(`✅ Test server listening on port ${port}`);
      
      // Clean up and exit
      setTimeout(async () => {
        console.log('🧹 Cleaning up...');
        httpServer.close();
        await pubClient.quit();
        await subClient.quit();
        console.log('✅ Test completed successfully!');
        console.log('\n🎉 Redis adapter configuration is working correctly');
        console.log('The original error should be fixed now.');
        process.exit(0);
      }, 1000);
    });
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run the test
testRedisAdapter().catch(console.error);
