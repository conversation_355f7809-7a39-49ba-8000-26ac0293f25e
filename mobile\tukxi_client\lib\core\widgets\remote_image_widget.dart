import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:tukxi/core/constants/asset_paths.dart';

class RemoteImageWidget extends StatelessWidget {
  final String? imageUrl;
  final double size;
  final String defaultAssetPath;
  final EdgeInsets? padding;
  final BoxBorder? border;
  final Color? backgroundColor;
  final double borderRadius;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;

  const RemoteImageWidget({
    super.key,
    this.imageUrl,
    this.size = 50,
    this.defaultAssetPath = AssetPaths.sedan,
    this.padding,
    this.border,
    this.backgroundColor,
    this.borderRadius = 25,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(borderRadius),
      child: _buildImage(),
    );
  }

  Widget _buildImage() {
    // Check if imageUrl is valid
    if (imageUrl == null || imageUrl!.isEmpty || !_isValidUrl(imageUrl!)) {
      return _buildDefaultImage();
    }

    return CachedNetworkImage(
      imageUrl: imageUrl!,
      fit: fit,
      placeholder: (context, url) => placeholder ?? _buildDefaultImage(),
      errorWidget: (context, url, error) => errorWidget ?? _buildDefaultImage(),
      fadeInDuration: const Duration(milliseconds: 300),
      fadeOutDuration: const Duration(milliseconds: 300),
    );
  }

  Widget _buildDefaultImage() {
    return Image.asset(
      defaultAssetPath,
      fit: fit,
      errorBuilder: (context, error, stackTrace) => _buildFallbackIcon(),
    );
  }

  Widget _buildFallbackIcon() {
    return Container(
      color: Colors.grey[300],
      child: Icon(Icons.person, size: size * 0.6, color: Colors.grey[600]),
    );
  }

  bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.isScheme('http') || uri.isScheme('https'));
    } catch (e) {
      return false;
    }
  }
}
