import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/location_constants.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/features/booking/data/models/driver.dart';
import 'package:tukxi/features/booking/data/models/ride.dart';
import 'package:tukxi/features/booking/data/models/ride_request.dart';
import 'package:tukxi/features/booking/presentation/widgets/driver_details_view.dart';
import 'package:tukxi/features/favourite_locations/data/models/location_response.dart';
import 'package:tukxi/features/location_selection/presentation/widgets/available_products/ride_location_view.dart';

class DriverAcceptedView extends StatelessWidget {
  const DriverAcceptedView({
    super.key,
    required this.rideRequest,
    required this.rideDetails,
    required this.driverDetails,
    this.onSize,
    required this.onMoreOptionsTapped,
  });

  final VoidCallback onMoreOptionsTapped;
  final RideRequest? rideRequest;
  final Ride? rideDetails;
  final Driver? driverDetails;
  final void Function(double height)? onSize;

  LocationResponse get pickupFromRequest {
    return LocationResponse(
      address: rideRequest?.pickupLoctaion.locationName ?? '',
      latitude: rideRequest?.pickupLoctaion.latLng?.latitude ?? 0,
      longitude: rideRequest?.pickupLoctaion.latLng?.longitude ?? 0,
    );
  }

  LocationResponse get destinationFromRequest {
    return LocationResponse(
      address: rideRequest?.destinationLoctaion.locationName ?? '',
      latitude: rideRequest?.destinationLoctaion.latLng?.latitude ?? 0,
      longitude: rideRequest?.destinationLoctaion.latLng?.longitude ?? 0,
    );
  }

  LocationResponse get pickup {
    return (rideRequest == null &&
            rideDetails != null &&
            rideDetails!.pickupLocation != null)
        ? rideDetails!.pickupLocation!
        : pickupFromRequest;
  }

  LocationResponse get destination {
    return (rideRequest == null &&
            rideDetails != null &&
            rideDetails!.destinationLocation != null)
        ? rideDetails!.destinationLocation!
        : destinationFromRequest;
  }

  String? get _otp {
    if (driverDetails != null) {
      if (driverDetails!.verificationCode == null) return null;
      return '${driverDetails!.verificationCode!}';
    } else if (rideDetails != null) {
      if (rideDetails!.rider == null || rideDetails!.rider!.rideOtp == null) {
        return null;
      }
      return rideDetails!.rider!.rideOtp;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    // final pickup = rideRequets?.pickupLoctaion;
    // final destination = rideRequets?.destinationLoctaion;

    return LayoutBuilder(
      builder: (ctx, constraints) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          final box = ctx.findRenderObject() as RenderBox;
          onSize?.call(box.size.height);
        });
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.only(left: 15, right: 15, top: 20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(LocationConstants.mapCornerRadius),
              topRight: Radius.circular(LocationConstants.mapCornerRadius),
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.black10,
                blurRadius: 1,
                offset: Offset(0, -1),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    child: Text(
                      'PIN for this trip',
                      style: GoogleFonts.inter(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  if (_otp != null) _buildOtpLabel(otp: _otp ?? '0'),
                ],
              ),
              const SizedBox(height: 20),

              RideLocationView(
                stops: [],
                pickupLocation: pickup,
                destinationLocation: destination,
              ),

              const SizedBox(height: 20),

              DriverDetailsView(
                onMoreOptionsTapped: onMoreOptionsTapped,
                driverDetails: driverDetails,
                rideDetails: rideDetails,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildOtpLabel({required String otp}) {
    final size = 30.0;
    if (otp == '0') otp = '0000';
    return Row(
      children: List.generate(otp.length, (i) {
        return Padding(
          padding: EdgeInsets.only(right: i == otp.length - 1 ? 0 : 5),
          child: Container(
            height: size,
            width: size,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(8),
            ),
            alignment: Alignment.center,
            child: Text(
              otp[i],
              textAlign: TextAlign.center,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
          ),
        );
      }),
    );
  }
}
