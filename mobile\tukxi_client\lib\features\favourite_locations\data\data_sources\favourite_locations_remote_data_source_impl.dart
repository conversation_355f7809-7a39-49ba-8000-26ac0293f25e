import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/network/api_service.dart';
import 'package:tukxi/features/favourite_locations/data/data_sources/favourite_locations_remote_data_sources.dart';
import 'package:tukxi/features/favourite_locations/data/models/favourite_location_response.dart';

class FavouriteRemoteDataSourceImpl implements FavouriteRemoteDataSource {
  final _apiService = ApiService();

  /// Fetch all favourite locations
  @override
  Future<FavoriteSuccessListResponse> getFavouriteLocations() async {
    try {
      final response = await _apiService.get(
        Endpoint.favouriteLocation.value,
        (json) => FavoriteSuccessListResponse.fromJson(json),
      );
      return response;
    } catch (error) {
      rethrow;
    }
  }

  /// Add a new favourite location
  @override
  Future<FavoriteSuccessResponse> addFavouriteLocation({
    required String name,
    required double latitude,
    required double longitude,
    required Map<String, dynamic> meta,
    String? description,
  }) async {
    try {
      final body = {
        'name': name,
        'location': {'lat': latitude, 'lng': longitude},
        'meta': {
          'address': meta['address'] as String?,
          'type': meta['type'] as String?,
        },
      };

      if (description != null && description.trim().isNotEmpty) {
        body['description'] = description;
      }

      final response = await _apiService.post(
        Endpoint.favouriteLocation.value,
        (json) => FavoriteSuccessResponse.fromJson(json),
        body: body,
      );
      return response;
    } catch (error) {
      rethrow;
    }
  }

  /// Edit an existing favourite location
  @override
  Future<FavoriteSuccessResponse> editFavouriteLocation({
    required String id,
    String? name,
    String? description,
    double? latitude,
    double? longitude,
    Map<String, dynamic>? meta,
  }) async {
    try {
      final body = {
        if (name != null && name.trim().isNotEmpty) 'name': name,
        if (description != null && description.trim().isNotEmpty)
          'description': description,
        if (latitude != null && longitude != null)
          'location': {'lat': latitude, 'lng': longitude},
        if (meta != null)
          'meta': {
            'address': meta['address'] as String?,
            'type': meta['type'] as String?,
          },
      };

      final response = await _apiService.patch(
        '${Endpoint.favouriteLocation.value}/$id',
        (json) => FavoriteSuccessResponse.fromJson(json),
        body: body,
      );
      return response;
    } catch (error) {
      rethrow;
    }
  }

  /// Delete a favourite location
  @override
  Future<FavoriteSuccessResponse> deleteFavouriteLocation({
    required String id,
  }) async {
    try {
      final response = await _apiService.delete(
        '${Endpoint.favouriteLocation.value}/$id',
        (json) => FavoriteSuccessResponse.fromJson(json),
      );
      // Assuming API returns { success: true } on successful deletion
      return response;
    } catch (error) {
      rethrow;
    }
  }
}
