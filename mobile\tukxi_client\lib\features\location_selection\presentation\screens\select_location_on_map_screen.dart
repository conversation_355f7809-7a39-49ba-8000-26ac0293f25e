import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/constants/location_constants.dart';
import 'package:tukxi/core/constants/ui_consants.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/services/google_place_service.dart';
import 'package:tukxi/core/services/location_service.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/core/widgets/nav_back_button.dart';
import 'package:tukxi/features/location_selection/presentation/providers/place_suggestions_provider.dart';
import 'package:tukxi/features/location_selection/presentation/widgets/select_location_on_map.dart/select_location_map_section.dart';
import 'package:go_router/go_router.dart';
import 'package:tukxi/features/location_selection/presentation/widgets/select_location_on_map.dart/select_location_on_map_draggable_view.dart';
import 'package:tukxi/features/home/<USER>/params/location_params.dart';

class SelectLocationOnMapScreen extends ConsumerStatefulWidget {
  const SelectLocationOnMapScreen({
    super.key,
    required this.locationType,
    required this.pickedLocation,
    required this.userLocation,
  });

  final LocationType locationType;
  final LocationParams? pickedLocation;
  final LatLng? userLocation;

  @override
  ConsumerState<SelectLocationOnMapScreen> createState() {
    return _SelectLocationOnMapScreenState();
  }
}

class _SelectLocationOnMapScreenState
    extends ConsumerState<SelectLocationOnMapScreen> {
  LatLng? _currentLocation;
  LatLng? _pickedLocation;
  GoogleMapController? _mapController;
  LocationParams? _selectedLocation;

  Timer? _debounce;
  // Keep track of last location we reverse-geocoded
  LatLng? _lastFetched;
  static const double _minDistance = 20;
  bool isLoading = true;
  //MARK: - Init State Method
  @override
  void initState() {
    super.initState();

    _pickedLocation = widget.pickedLocation?.latLng;
    _currentLocation = _pickedLocation ?? widget.userLocation;
    _selectedLocation = widget.pickedLocation;

    _refreshLocationInBackground();
  }

  //MARK: - Dispose
  @override
  void dispose() {
    _debounce?.cancel();
    _mapController?.dispose();
    super.dispose();
  }

  //MARK: - Build  Method
  @override
  Widget build(BuildContext context) {
    final totalHeight = MediaQuery.of(context).size.height;
    final bottomSpace = MediaQuery.of(context).padding.bottom;
    final availableHeight = totalHeight - bottomSpace;
    final mapHeight =
        UIConstants.locationSheetMapSize * availableHeight +
        LocationConstants.mapCornerRadius;

    return Scaffold(
      body: SafeArea(
        top: false,
        bottom: Platform.isAndroid,
        child: Column(
          children: [
            Expanded(
              child: Stack(
                children: [
                  SelectLocationMapSection(
                    currentLocation: _pickedLocation ?? _currentLocation,
                    mapHeight: mapHeight,
                    onMapCreated: (controller) => _mapController = controller,
                    onCameraIdle: _onCameraIdle,
                    onCameraMove: _onCameraMove,
                  ),

                  Positioned(
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: SelectLocationOnMapDraggableView(
                      locationType: widget.locationType,
                      isLoading: isLoading,
                      selectedLocation: _selectedLocation,
                      onLocationConfirm: (selectedLocation, locationType) {
                        if (selectedLocation == null || !mounted) return;
                        context.pop({
                          'selectedLocation': selectedLocation,
                          'locationType': locationType,
                        });
                      },
                    ),
                  ),

                  Positioned(
                    left: 5,
                    child: SafeArea(
                      child: NavBackButton(onPressed: () => context.pop()),
                    ),
                  ),

                  Positioned(
                    top: 50,
                    left: 16,
                    right: 16,
                    child: SafeArea(
                      child: FilledButton(
                        style: FilledButton.styleFrom(
                          backgroundColor: Colors.white,
                          padding: const EdgeInsets.only(
                            left: 10,
                            top: 5,
                            bottom: 5,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                            side: BorderSide(color: AppColors.black20),
                          ),
                        ),
                        onPressed: () {
                          context.pop({'locationType': widget.locationType});
                        },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SvgPicture.asset(
                              AssetPaths.search,
                              height: 20,
                              width: 20,
                              colorFilter: const ColorFilter.mode(
                                AppColors.black50,
                                BlendMode.srcIn,
                              ),
                            ),

                            const SizedBox(width: 15),

                            Text(
                              widget.locationType == LocationType.pickup
                                  ? 'Search pickup location'
                                  : 'Search destination location',
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: AppColors.black50,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///MARK: - Other Methods

  void _onCameraIdle() {
    if (_pickedLocation == null) return;

    // Skip if no significant movement since last fetch
    if (_lastFetched != null) {
      final dist = GooglePlacesService.calculateDistance(
        _lastFetched!.latitude,
        _lastFetched!.longitude,
        _pickedLocation!.latitude,
        _pickedLocation!.longitude,
      );
      if (dist < _minDistance) return;
    }

    // Debounce API call
    _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 400), () {
      if (!mounted) return;
      _fetchPlaceDetails(coordinates: _pickedLocation);
    });
  }

  void _onCameraMove(CameraPosition position) {
    // Update picked location continuously while user drags
    _pickedLocation = position.target;
    setState(() {
      isLoading = true;
    });
  }

  ///MARK: - API Call
  Future _fetchPlaceDetails({required LatLng? coordinates}) async {
    if (!mounted) return;
    final result = await ref
        .read(placeSuggestionsProvider.notifier)
        .getPlaceDetailsFromCoordinates(coordinates);

    result.fold(
      (failure) {
        if (!mounted) return;

        handleApiError(
          context: context,
          failure: failure,
          onRetry: () async => _fetchPlaceDetails(coordinates: coordinates),
        );
      },
      (placeDetails) {
        if (!mounted) return;
        setState(() {
          _selectedLocation = placeDetails;
          _lastFetched = coordinates;
          isLoading = false; // mark as last successful fetch
        });
      },
    );
  }

  /// MARK: - Background Location Refresh
  Future<void> _refreshLocationInBackground() async {
    if (!mounted) return;
    try {
      final updatedPosition = await LocationService.getCurrentPosition();
      if (!mounted) return;

      if (updatedPosition != null) {
        final distance = GooglePlacesService.calculateDistance(
          updatedPosition.latitude,
          updatedPosition.longitude,
          _currentLocation!.latitude,
          _currentLocation!.longitude,
        );
        // Update map if moved significantly
        if (distance > 20 && _pickedLocation == null) {
          final updatedLocation = LatLng(
            updatedPosition.latitude,
            updatedPosition.longitude,
          );
          setState(() {
            _currentLocation = updatedLocation;
            _pickedLocation ??= updatedLocation;

            _mapController?.animateCamera(
              CameraUpdate.newLatLng(updatedLocation),
            );
          });
        }
      }
    } catch (e) {
      debugPrint('GPS refresh failed: $e');
    }
  }
}
