import { Module } from '@nestjs/common';
import { TaxGroupService } from './tax-group.service';
import { TaxGroupRepository } from '../../repositories/tax-group.repository';
import { TaxSubcategoryRepository } from '../../repositories/tax-subcategory.repository';
import { RepositoryModule } from '../../repositories/repository.module';

@Module({
  imports: [RepositoryModule],
  providers: [TaxGroupService, TaxGroupRepository, TaxSubcategoryRepository],
  exports: [TaxGroupService],
})
export class TaxGroupModule {}
