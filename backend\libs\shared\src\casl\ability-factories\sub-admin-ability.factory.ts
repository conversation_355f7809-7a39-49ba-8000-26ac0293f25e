import {
  AbilityBuilder,
  ExtractSubjectType,
  MongoAbility,
  createMongoAbility,
} from '@casl/ability';
import { Injectable } from '@nestjs/common';
import { ApiConsumer } from '@shared/shared/modules/auth/interfaces';

type Action = 'create' | 'list' | 'edit' | 'status_update';

type Subjects = 'SubAdmin' | 'all';

export type SubAdminAbility = MongoAbility<[Action, Subjects]>;

@Injectable()
export class SubAdminAbilityFactory {
  constructor() {}

  async createForAPIConsumer(apiConsumer: ApiConsumer) {
    const { can, build } = new AbilityBuilder(createMongoAbility);

    // Get user permissions from token
    const userPermissions = apiConsumer.permissions || [];

    // Define permissions based on token permissions

    // subAdmin:create permission
    if (userPermissions.includes('subAdmin:create')) {
      can('create', 'SubAdmin');
    }

    // subAdmin:list permission
    if (userPermissions.includes('subAdmin:list')) {
      can('list', 'SubAdmin');
    }

    // subAdmin:edit permission
    if (userPermissions.includes('subAdmin:edit')) {
      can('edit', 'SubAdmin');
    }

    // subAdmin:status_update permission
    if (userPermissions.includes('subAdmin:status_update')) {
      can('status_update', 'SubAdmin');
    }

    return build({
      detectSubjectType: (item: any) =>
        item.constructor as ExtractSubjectType<Subjects>,
    });
  }
}
