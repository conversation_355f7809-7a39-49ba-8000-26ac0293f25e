import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:tukxi/core/theme/app_colors.dart';

class ShimmerLoaders {
  ///Shimer for vertical title and value

  static Widget textShimmer() {
    return Shimmer.fromColors(
      baseColor: AppColors.black20,
      highlightColor: AppColors.black10,
      child: Container(
        width: double.infinity,
        height: 16,
        decoration: BoxDecoration(
          color: AppColors.black40,
          borderRadius: BorderRadius.circular(4),
        ),
      ),
    );
  }

  static Widget profileScreenShimmer() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // final spacing = 16.0;
        // final length = 3;
        // final margin = 20;
        // final totalMargin = margin * 2;
        // final totalSpacing = spacing * (length - 1);
        // final cardWidth =
        //     (constraints.maxWidth - totalSpacing - totalMargin) / length;

        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Shimmer.fromColors(
                    baseColor: AppColors.black20,
                    highlightColor: AppColors.black10,
                    child: Container(
                      width: 64,
                      height: 64,
                      decoration: BoxDecoration(
                        color: AppColors.black40,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                  SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      children: [
                        Shimmer.fromColors(
                          baseColor: AppColors.black20,
                          highlightColor: AppColors.black10,
                          child: Container(
                            height: 16,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: AppColors.black40,
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Shimmer.fromColors(
                          baseColor: AppColors.black20,
                          highlightColor: AppColors.black10,
                          child: Container(
                            height: 16,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: AppColors.black40,
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 30),

              // Row(
              //   children: List.generate(
              //     length,
              //     (index) => _profileCardShimmer(
              //       cardWidth,
              //       spacing,
              //       index == length - 1,
              //     ),
              //   ),
              // ),

              // SizedBox(height: 20),
              ...List.generate(2, (index) => _profileOptionsShimmer()),
            ],
          ),
        );
      },
    );
  }

  static Widget _profileOptionsShimmer() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Shimmer.fromColors(
          baseColor: AppColors.black20,
          highlightColor: AppColors.black10,
          child: Container(
            height: 16,
            width: 200,
            decoration: BoxDecoration(
              color: AppColors.black40,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),

        SizedBox(height: 20),

        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(width: 1, color: AppColors.black10),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: List.generate(5, (index) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Row(
                      children: [
                        Expanded(
                          child: Shimmer.fromColors(
                            baseColor: AppColors.black20,
                            highlightColor: AppColors.black10,
                            child: Container(
                              height: 16,
                              width: 200,
                              decoration: BoxDecoration(
                                color: AppColors.black40,
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),

                        SizedBox(width: 10),

                        Icon(
                          Icons.arrow_forward_ios,
                          size: 16,
                          color: AppColors.black50,
                        ),
                      ],
                    ),
                  ),

                  if (index != 4)
                    const Divider(
                      indent: 0,
                      endIndent: 0,
                      thickness: 1,
                      height: 1,
                      color: AppColors.black10,
                    ),
                ],
              );
            }).toList(),
          ),
        ),
        SizedBox(height: 20),
      ],
    );
  }
  // static Widget _profileCardShimmer(
  //   double cardWidth,
  //   double spacing,
  //   bool isLast,
  // ) {
  //   return Row(
  //     children: [
  //       Container(
  //         padding: const EdgeInsets.all(16),
  //         width: cardWidth,
  //         decoration: BoxDecoration(
  //           color: AppColors.profileCardGreyBg,
  //           borderRadius: BorderRadius.circular(16),
  //         ),
  //         child: Column(
  //           children: [
  //             Shimmer.fromColors(
  //               baseColor: AppColors.black20,
  //               highlightColor: AppColors.black10,
  //               child: Container(
  //                 height: 16,
  //                 width: double.infinity,
  //                 decoration: BoxDecoration(
  //                   color: AppColors.black40,
  //                   borderRadius: BorderRadius.circular(8),
  //                 ),
  //               ),
  //             ),

  //             const SizedBox(height: 20),

  //             Shimmer.fromColors(
  //               baseColor: AppColors.black20,
  //               highlightColor: AppColors.black10,
  //               child: Container(
  //                 height: 16,
  //                 width: double.infinity,
  //                 decoration: BoxDecoration(
  //                   color: AppColors.black40,
  //                   borderRadius: BorderRadius.circular(8),
  //                 ),
  //               ),
  //             ),
  //           ],
  //         ),
  //       ),

  //       if (!isLast) SizedBox(width: spacing),
  //     ],
  //   );
  // }

  static Widget accountDetailsSimmer() {
    return Column(
      children: [
        const Divider(thickness: 1, height: 1, color: AppColors.black10),

        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: SingleChildScrollView(
              child: Column(
                children: [
                  const SizedBox(height: 25),
                  // Profile image shimmer
                  Shimmer.fromColors(
                    baseColor: AppColors.black20,
                    highlightColor: AppColors.black10,
                    child: Container(
                      width: 128,
                      height: 128,
                      decoration: BoxDecoration(
                        color: AppColors.black40,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),

                  const SizedBox(height: 20),
                  // Personal info title shimmer
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Shimmer.fromColors(
                      baseColor: AppColors.black20,
                      highlightColor: AppColors.black10,
                      child: Container(
                        width: 160,
                        height: 20,
                        margin: const EdgeInsets.only(bottom: 20),
                        decoration: BoxDecoration(
                          color: AppColors.black40,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ),
                  ),

                  // Fields shimmer
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                      vertical: 16,
                      horizontal: 16,
                    ),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.black10),
                      borderRadius: BorderRadius.circular(12),
                    ),

                    child: Column(
                      children: List.generate(5, (index) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Shimmer.fromColors(
                              baseColor: AppColors.black20,
                              highlightColor: AppColors.black10,
                              child: Container(
                                height: 16,
                                width: 200,
                                decoration: BoxDecoration(
                                  color: AppColors.black40,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Shimmer.fromColors(
                              baseColor: AppColors.black20,
                              highlightColor: AppColors.black10,
                              child: Container(
                                height: 16,
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: AppColors.black40,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                            ),
                            if (index != 4)
                              const Divider(
                                indent: 0,
                                endIndent: 0,
                                thickness: 1,
                                height: 20,
                                color: AppColors.black10,
                              ),
                          ],
                        );
                      }),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  static Widget titleAndValueShimmer() {
    return Shimmer.fromColors(
      baseColor: AppColors.black20,
      highlightColor: AppColors.black10,
      child: Column(
        children: [
          Container(
            width: double.infinity,
            height: 16,
            decoration: BoxDecoration(
              color: AppColors.black40,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          SizedBox(height: 4),
          Container(
            width: double.infinity,
            height: 16,
            decoration: BoxDecoration(
              color: AppColors.black40,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }

  /// Shimmer for Trips card
  static Widget tripCardShimmer() {
    return Shimmer.fromColors(
      baseColor: AppColors.black20,
      highlightColor: AppColors.black10,
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.black15,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.black10),
        ),
        child: Row(
          children: [
            // Car icon placeholder
            Container(
              width: 65,
              height: 65,
              decoration: BoxDecoration(
                color: AppColors.black40,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            const SizedBox(width: 12),

            // Text content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Container(
                    width: double.infinity,
                    height: 12,
                    decoration: BoxDecoration(
                      color: AppColors.black40,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const SizedBox(height: 8),
                  // Subtitle
                  Container(
                    width: 120,
                    height: 10,
                    decoration: BoxDecoration(
                      color: AppColors.black40,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const SizedBox(height: 8),

                  Container(
                    width: 120,
                    height: 10,
                    decoration: BoxDecoration(
                      color: AppColors.black40,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Shimmer for ride option cards
  static Widget rideOptionShimmer() {
    return Shimmer.fromColors(
      baseColor: AppColors.black20,
      highlightColor: AppColors.black10,
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.black15,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.black10),
        ),
        child: Row(
          children: [
            // Car icon placeholder
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: AppColors.black40,
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            const SizedBox(width: 12),

            // Text content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Container(
                    width: double.infinity,
                    height: 16,
                    decoration: BoxDecoration(
                      color: AppColors.black40,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  const SizedBox(height: 8),
                  // Subtitle
                  Container(
                    width: 120,
                    height: 12,
                    decoration: BoxDecoration(
                      color: AppColors.black40,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  static Widget tripDetailsShimmer() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          const SizedBox(height: 30),
          Shimmer.fromColors(
            baseColor: AppColors.black20,
            highlightColor: AppColors.black10,
            child: Column(
              children: [
                // Driver image shimmer
                CircleAvatar(radius: 32, backgroundColor: Colors.white),
                const SizedBox(height: 12),
                Container(
                  height: 20,
                  width: 120,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  height: 12,
                  width: 80,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                const SizedBox(height: 20),
                // Actions row shimmer
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      height: 36,
                      width: 80,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Container(
                      height: 36,
                      width: 80,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                // Map shimmer
                Container(
                  height: 160,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                // Trip itinerary shimmer
                const SizedBox(height: 16),
                ...List.generate(
                  3,
                  (i) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Container(
                      height: 18,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Additional details shimmer
              ],
            ),
          ),
        ],
      ),
    );
  }
}
