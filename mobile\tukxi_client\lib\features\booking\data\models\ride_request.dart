import 'package:tukxi/features/home/<USER>/params/location_params.dart';
import 'package:tukxi/features/location_selection/data/models/available_option.dart';

class RideRequest {
  const RideRequest({
    required this.pickupLoctaion,
    required this.destinationLoctaion,
    required this.service,
    this.stops,
  });

  final LocationParams pickupLoctaion;
  final LocationParams destinationLoctaion;
  final List<LocationParams>? stops;
  final AvailableOptionData service;
}
