// Delete favourite location
import 'package:dartz/dartz.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/features/favourite_locations/data/models/favourite_location_response.dart';
import 'package:tukxi/features/favourite_locations/domain/repositories/favourite_location_repository.dart';

class DeleteFavouriteLocationUseCase {
  final FavouriteRepository repository;
  DeleteFavouriteLocationUseCase(this.repository);

  Future<Either<Failure, FavoriteSuccessResponse>> execute({
    required String id,
  }) {
    return repository.deleteFavouriteLocation(id: id);
  }
}
