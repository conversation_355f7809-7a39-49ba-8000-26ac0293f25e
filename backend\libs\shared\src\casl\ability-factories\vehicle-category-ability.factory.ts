import {
  AbilityBuilder,
  ExtractSubjectType,
  MongoAbility,
  createMongoAbility,
} from '@casl/ability';
import { Injectable } from '@nestjs/common';
import { ApiConsumer } from '@shared/shared/modules/auth/interfaces';

type Action = 'create' | 'list' | 'edit' | 'delete';

type Subjects = 'VehicleCategory' | 'all';

export type VehicleCategoryAbility = MongoAbility<[Action, Subjects]>;

@Injectable()
export class VehicleCategoryAbilityFactory {
  constructor() {}

  async createForAPIConsumer(apiConsumer: ApiConsumer) {
    const { can, build } = new AbilityBuilder(createMongoAbility);

    // Get user permissions from token
    const userPermissions = apiConsumer.permissions || [];

    // Define permissions based on token permissions

    // vehicle_category:create permission
    if (userPermissions.includes('vehicle_category:create')) {
      can('create', 'VehicleCategory');
    }

    // vehicle_category:list permission
    if (userPermissions.includes('vehicle_category:list')) {
      can('list', 'VehicleCategory');
    }

    // vehicle_category:edit permission
    if (userPermissions.includes('vehicle_category:edit')) {
      can('edit', 'VehicleCategory');
    }

    // vehicle_category:delete permission
    if (userPermissions.includes('vehicle_category:delete')) {
      can('delete', 'VehicleCategory');
    }

    return build({
      detectSubjectType: (item: any) =>
        item.constructor as ExtractSubjectType<Subjects>,
    });
  }
}
