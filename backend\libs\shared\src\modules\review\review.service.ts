import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { ReviewRepository } from '../../repositories/review.repository';
import { RideRepository } from '../../repositories/ride.repository';
import { UserProfileRepository } from '../../repositories/user-profile.repository';
import { UserMetaDataService } from '../user-meta-data/user-meta-data.service';
import {
  Review,
  ReviewWithRelations,
  CreateReviewData,
  UpdateReviewData,
  ReviewFilters,
  ReviewStats,
} from '../../repositories/models/review.model';
import { RideStatus } from '../ride-matching/constants';

@Injectable()
export class ReviewService {
  private readonly logger = new Logger(ReviewService.name);

  constructor(
    private readonly reviewRepository: ReviewRepository,
    private readonly rideRepository: RideRepository,
    private readonly userProfileRepository: UserProfileRepository,
    private readonly userMetaDataService: UserMetaDataService,
  ) {}

  /**
   * Create a review for a ride
   */
  async createReview(data: CreateReviewData): Promise<Review> {
    this.logger.log(
      `Creating review for ride ${data.rideId} by user ${data.reviewById}`,
    );

    // Validate the ride exists and is completed
    const ride = await this.rideRepository.findById(data.rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${data.rideId} not found`);
    }

    // Check if ride is completed
    if (ride.status !== RideStatus.TRIP_COMPLETED) {
      throw new BadRequestException(
        'Reviews can only be submitted for completed rides',
      );
    }

    // Validate that the reviewer is either the rider or driver of this ride
    if (data.reviewById !== ride.riderId && data.reviewById !== ride.driverId) {
      throw new ForbiddenException(
        'You can only review rides you participated in',
      );
    }

    // Validate that the reviewer hasn't already reviewed this ride
    const existingReview = await this.reviewRepository.hasUserReviewedRide(
      data.rideId,
      data.reviewById,
    );
    if (existingReview) {
      throw new BadRequestException('You have already reviewed this ride');
    }

    // Validate rating is between 1 and 5
    if (data.rating < 1 || data.rating > 5) {
      throw new BadRequestException('Rating must be between 1 and 5');
    }

    // Validate that rider and driver exist
    const [rider, driver] = await Promise.all([
      this.userProfileRepository.findById(data.riderId),
      this.userProfileRepository.findById(data.driverId),
    ]);

    if (!rider) {
      throw new NotFoundException(`Rider with ID ${data.riderId} not found`);
    }

    if (!driver) {
      throw new NotFoundException(`Driver with ID ${data.driverId} not found`);
    }

    const review = await this.reviewRepository.createReview(data);
    this.logger.log(`Review created successfully with ID ${review.id}`);

    // Update average rating for the reviewed user
    const reviewedUserId =
      data.reviewById === data.riderId ? data.driverId : data.riderId;
    const reviewedUserRole =
      data.reviewById === data.riderId ? 'driver' : 'rider';

    // Update metadata asynchronously (don't wait for it to complete)
    this.userMetaDataService
      .handleNewReview(reviewedUserId, reviewedUserRole)
      .catch((error) => {
        this.logger.error(
          `Failed to update metadata after review creation: ${error.message}`,
        );
      });

    return review;
  }

  /**
   * Driver reviews rider after a completed ride
   */
  async driverReviewRider(
    rideId: string,
    driverId: string,
    rating: number,
    reviewText?: string,
  ): Promise<Review> {
    this.logger.log(`Driver ${driverId} reviewing rider for ride ${rideId}`);

    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    // Validate that the driver is assigned to this ride
    if (ride.driverId !== driverId) {
      throw new ForbiddenException(
        'You can only review rides you were assigned to',
      );
    }

    const reviewData: CreateReviewData = {
      riderId: ride.riderId,
      driverId: ride.driverId!,
      rideId,
      reviewById: driverId,
      rating,
      review: reviewText || undefined,
    };

    return this.createReview(reviewData);
  }

  /**
   * Rider reviews driver after a completed ride
   */
  async riderReviewDriver(
    rideId: string,
    riderId: string,
    rating: number,
    reviewText?: string,
  ): Promise<Review> {
    this.logger.log(`Rider ${riderId} reviewing driver for ride ${rideId}`);

    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    // Validate that the rider is the one who requested this ride
    if (ride.riderId !== riderId) {
      throw new ForbiddenException('You can only review your own rides');
    }

    if (!ride.driverId) {
      throw new BadRequestException(
        'Cannot review a ride without an assigned driver',
      );
    }

    const reviewData: CreateReviewData = {
      riderId: ride.riderId,
      driverId: ride.driverId,
      rideId,
      reviewById: riderId,
      rating,
      review: reviewText || undefined,
    };

    return this.createReview(reviewData);
  }

  /**
   * Get review by ID
   */
  async getReviewById(id: string): Promise<ReviewWithRelations> {
    const review = await this.reviewRepository.findReviewByIdWithRelations(id);
    if (!review) {
      throw new NotFoundException(`Review with ID ${id} not found`);
    }
    return review;
  }

  /**
   * Get reviews with filters and pagination
   */
  async getReviews(
    filters: ReviewFilters,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    reviews: ReviewWithRelations[];
    total: number;
    page: number;
    limit: number;
  }> {
    const result = await this.reviewRepository.findReviewsWithFilters(
      filters,
      page,
      limit,
    );
    return {
      ...result,
      page,
      limit,
    };
  }

  /**
   * Get reviews for a specific user (as driver or rider)
   */
  async getUserReviews(
    userId: string,
    asRole: 'driver' | 'rider',
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    reviews: ReviewWithRelations[];
    total: number;
    page: number;
    limit: number;
  }> {
    const filters: ReviewFilters = {};
    if (asRole === 'driver') {
      filters.driverId = userId;
    } else {
      filters.riderId = userId;
    }

    return this.getReviews(filters, page, limit);
  }

  /**
   * Get review statistics for a user
   */
  async getUserReviewStats(
    userId: string,
    asRole: 'driver' | 'rider',
  ): Promise<ReviewStats> {
    return this.reviewRepository.getUserReviewStats(userId, asRole);
  }

  /**
   * Update a review (only by the reviewer)
   */
  async updateReview(
    reviewId: string,
    userId: string,
    data: UpdateReviewData,
  ): Promise<Review> {
    const review = await this.reviewRepository.findById(reviewId);
    if (!review) {
      throw new NotFoundException(`Review with ID ${reviewId} not found`);
    }

    // Only the reviewer can update their review
    if (review.reviewById !== userId) {
      throw new ForbiddenException('You can only update your own reviews');
    }

    // Validate rating if provided
    if (data.rating !== undefined && (data.rating < 1 || data.rating > 5)) {
      throw new BadRequestException('Rating must be between 1 and 5');
    }

    return this.reviewRepository.updateReview(reviewId, data);
  }

  /**
   * Get reviews for a specific ride
   */
  async getReviewsForRide(rideId: string): Promise<ReviewWithRelations[]> {
    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      throw new NotFoundException(`Ride with ID ${rideId} not found`);
    }

    return this.reviewRepository.getReviewsForRide(rideId);
  }

  /**
   * Check if user can review a ride
   */
  async canUserReviewRide(rideId: string, userId: string): Promise<boolean> {
    const ride = await this.rideRepository.findById(rideId);
    if (!ride) {
      return false;
    }

    // Check if ride is completed
    if (ride.status !== 'trip_completed') {
      return false;
    }

    // Check if user is participant in the ride
    if (userId !== ride.riderId && userId !== ride.driverId) {
      return false;
    }

    // Check if user has already reviewed
    const hasReviewed = await this.reviewRepository.hasUserReviewedRide(
      rideId,
      userId,
    );
    return !hasReviewed;
  }
}
