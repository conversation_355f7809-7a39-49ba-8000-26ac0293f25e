import 'dart:io';
import 'package:dio/dio.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/network/api_service.dart';
import 'package:tukxi/features/profile/data/data_sources/profile_remote_data_sourc.dart';
import 'package:tukxi/features/profile/data/models/file_upload_response.dart';
import 'package:path/path.dart' as path;

class ProfileRemoteDataSourceImpl implements ProfileRemoteDataSource {
  final _apiService = ApiService();

  @override
  Future<FileUploadResponse> uploadProfileFile({required File file}) async {
    Map<String, dynamic> body = {};

    String extension = path.extension(file.path);
    final contentType = _getContentTypeFromExtension(extension);

    body['file'] = await MultipartFile.fromFile(
      file.path,
      filename: 'image$extension',
      contentType: DioMediaType.parse(contentType),
    );

    body['isPublic'] = true;

    print(body);
    print(file.path);

    FormData formData = FormData.fromMap(body);
    try {
      final response = await _apiService.post(
        Endpoint.fileUpload.value,
        (json) => FileUploadResponse.fromJson(json),
        extraHeaders: {},
        body: formData,
      );
      return response;
    } catch (error) {
      rethrow;
    }
  }

  String _getContentTypeFromExtension(String extension) {
    switch (extension.toLowerCase()) {
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.png':
        return 'image/png';
      case '.gif':
        return 'image/gif';
      case '.webp':
        return 'image/webp';
      case '.heic':
        return 'image/heic';
      default:
        return 'application/octet-stream'; // Fallback for unknown types
    }
  }
}
