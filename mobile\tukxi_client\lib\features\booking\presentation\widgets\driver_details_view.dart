import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/widgets/circular_image_widget.dart';
import 'package:tukxi/features/booking/data/models/driver.dart';
import 'package:tukxi/features/booking/data/models/ride.dart';

class DriverDetailsView extends StatelessWidget {
  const DriverDetailsView({
    super.key,
    required this.onMoreOptionsTapped,
    required this.driverDetails,
    required this.rideDetails,
  });

  final VoidCallback onMoreOptionsTapped;
  final Driver? driverDetails;
  final Ride? rideDetails;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.black10),
      ),
      child: Column(
        children: [
          _buildDriverDetails(),
          const SizedBox(height: 20),
          _buildBottomView(),
          const SizedBox(height: 10),
        ],
      ),
    );
  }

  Widget _buildDriverDetails() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Avatar
        CircularImageWidget(
          size: 48,
          borderRadius: 24,
          imageUrl: driverDetails?.profilePic,
        ),

        const SizedBox(width: 15),

        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                rideDetails?.vehicle?.vehicleNumber ??
                    driverDetails?.vehicleRegNumber ??
                    '',
                style: GoogleFonts.inter(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 3),
              Text(
                rideDetails?.vehicle?.vehicleType?.name ??
                    driverDetails?.vehicleModel ??
                    '',
                style: GoogleFonts.inter(
                  fontWeight: FontWeight.w500,
                  fontSize: 10,
                  color: AppColors.black50,
                ),
              ),
            ],
          ),
        ),

        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              driverName(),
              style: GoogleFonts.inter(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 3),
            Row(
              children: [
                Image.asset(AssetPaths.star, height: 15),
                const SizedBox(width: 5),
                Text(
                  '${driverDetails?.driverRating}/5',
                  style: GoogleFonts.inter(
                    fontWeight: FontWeight.w600,
                    fontSize: 10,
                    color: AppColors.black50,
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBottomView() {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 48,
            padding: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: AppColors.greyBg,
              borderRadius: BorderRadius.circular(12),
            ),
            alignment: Alignment.center,
            child: Text(
              'Message Driver',
              style: GoogleFonts.inter(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ),
        ),
        const SizedBox(width: 8),

        // Call button
        SizedBox(
          width: 48,
          height: 48,
          child: FilledButton(
            onPressed: () {},
            style: FilledButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 2),
              backgroundColor: AppColors.greyBg,
              foregroundColor: Colors.black,
            ),
            child: SvgPicture.asset(AssetPaths.phone, height: 25),
          ),
        ),

        const SizedBox(width: 8),

        // More button
        SizedBox(
          width: 48,
          height: 48,
          child: FilledButton(
            onPressed: onMoreOptionsTapped,
            style: FilledButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 2),
              backgroundColor: AppColors.greyBg,
              foregroundColor: Colors.black,
            ),
            child: const Icon(Icons.more_horiz, size: 25),
          ),
        ),
      ],
    );
  }

  String driverName() {
    if (driverDetails != null) {
      return '${driverDetails?.firstName ?? ''} ${driverDetails?.lastName ?? ''}';
    } else if (rideDetails != null) {
      return '${rideDetails?.driver?.firstName ?? ''} ${rideDetails?.driver?.lastName ?? ''}';
    } else {
      return 'Unknown';
    }
  }
}
