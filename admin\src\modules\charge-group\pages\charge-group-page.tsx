'use client';

import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useState, useMemo } from 'react';
import { useListChargeGroup } from '../api/queries';
import { ChargeGroupFilters } from '../components/charge-group-filters';
import { ChargeGroupTable } from '../components/charge-group-table';
import { ChargeFilters } from '../components/charge-filters';
import { ChargeTable } from '../components/charge-table';
import { ChargeModal } from '../components/charge-modal';
import { AttachChargeModal } from '../components/attach-charge-modal';
import { useListCharges } from '../api/charge-queries';
import { ChargeGroup } from '../types/charge-group';
import { ArrowLeft } from 'lucide-react';

export function ChargeGroupPage() {
   const [page, setPage] = useState(1);
   const [limit] = useState(10);
   const [search, setSearch] = useState('');
   const [selectedChargeGroup, setSelectedChargeGroup] = useState<ChargeGroup | null>(null);
   const [chargeSearch, setChargeSearch] = useState('');
   const [isCreateChargeModalOpen, setIsCreateChargeModalOpen] = useState(false);
   const [isAttachChargeModalOpen, setIsAttachChargeModalOpen] = useState(false);

   // Reset to first page when filters change
   const handleSearchChange = (value: string) => {
      setSearch(value);
      setPage(1);
   };

   // Function to clear all filters
   const handleClearFilters = () => {
      setSearch('');
      setPage(1);
   };

   const listChargeGroupQuery = useListChargeGroup({
      page,
      limit,
      search: search || undefined,
      sortBy: 'createdAt',
      sortOrder: 'desc',
   });

   // Query charges for selected charge group
   const listChargesQuery = useListCharges(selectedChargeGroup?.id || null);

   // Filter charges based on search
   const filteredCharges = useMemo(() => {
      if (!chargeSearch || !listChargesQuery.data?.data) {
         return listChargesQuery.data?.data;
      }
      return listChargesQuery.data.data.filter((charge) =>
         charge.name.toLowerCase().includes(chargeSearch.toLowerCase()) ||
         charge.identifier?.toLowerCase().includes(chargeSearch.toLowerCase())
      );
   }, [listChargesQuery.data?.data, chargeSearch]);

   const handleManageChargeGroup = (chargeGroup: ChargeGroup) => {
      setSelectedChargeGroup(chargeGroup);
      setChargeSearch('');
   };

   const handleBackToList = () => {
      setSelectedChargeGroup(null);
      setChargeSearch('');
   };

   // Render manage view
   if (selectedChargeGroup) {
      return (
         <div className='flex flex-1 flex-col gap-4 p-6'>
            <div className='flex items-center gap-3'>
               <Button
                  variant='ghost'
                  size='sm'
                  onClick={handleBackToList}
                  className='cursor-pointer'
               >
                  <ArrowLeft className='h-4 w-4' />
               </Button>
               <h2 className='text-2xl font-semibold text-gray-900'>
                  Manage {selectedChargeGroup.name}
               </h2>
            </div>

            <Card className='overflow-hidden py-4 px-4 rounded-sm'>
               <ChargeFilters
                  search={chargeSearch}
                  onSearchChange={setChargeSearch}
                  isLoading={listChargesQuery.isFetching}
                  onAddCharge={() => setIsCreateChargeModalOpen(true)}
                  onAttachCharge={() => setIsAttachChargeModalOpen(true)}
               />

               <ChargeTable
                  chargeGroupId={selectedChargeGroup.id}
                  data={filteredCharges}
                  isLoading={listChargesQuery.isLoading}
               />
            </Card>

            {/* Create Charge Modal */}
            <ChargeModal
               mode='create'
               chargeGroupId={selectedChargeGroup.id}
               isOpen={isCreateChargeModalOpen}
               onClose={() => setIsCreateChargeModalOpen(false)}
            />

            {/* Add Charge Modal */}
            <AttachChargeModal
               chargeGroupId={selectedChargeGroup.id}
               isOpen={isAttachChargeModalOpen}
               onClose={() => setIsAttachChargeModalOpen(false)}
            />
         </div>
      );
   }

   // Render list view
   return (
      <div className='flex flex-1 flex-col gap-4 p-6'>
         <div className='flex justify-between items-center'>
            <h2 className='text-2xl font-semibold text-gray-900'>Charge Groups</h2>
         </div>

         <Card className='overflow-hidden py-4 px-4 rounded-sm'>
            <ChargeGroupFilters
               search={search}
               onSearchChange={handleSearchChange}
               isLoading={listChargeGroupQuery.isFetching && !listChargeGroupQuery.isLoading}
            />

            <ChargeGroupTable
               data={listChargeGroupQuery.data}
               isLoading={listChargeGroupQuery.isLoading}
               currentPage={page}
               onPageChange={(newPage: number) => setPage(newPage)}
               hasFilters={!!search}
               hasSearch={!!search}
               onClearFilters={handleClearFilters}
               onManageChargeGroup={handleManageChargeGroup}
            />
         </Card>
      </div>
   );
}
