import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/constants/location_constants.dart';
import 'package:tukxi/features/favourite_locations/presentation/widgets/search_location_field_button.dart';
import 'package:tukxi/features/location_selection/presentation/providers/place_suggestions_provider.dart';

class FavouriteMapSection extends ConsumerWidget {
  const FavouriteMapSection({
    super.key,
    required this.mapHeight,
    required this.onMapCreated,
    required this.isUserLocationEnabled,
    this.searchTapped,
    this.isFromFav = true,
    this.currentLocation,
    this.onCameraIdle,
    this.onCameraMove,
    this.hasOverlayElements = false,
  });

  final double mapHeight;
  final LatLng? currentLocation;
  final bool isUserLocationEnabled;
  final bool isFromFav;
  final bool hasOverlayElements;
  final VoidCallback? searchTapped;
  final void Function()? onCameraIdle;
  final void Function(GoogleMapController controller) onMapCreated;
  final void Function(CameraPosition position)? onCameraMove;
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bottomPadding = ref.watch(mapPaddingProvider);
    final pinHeight = 40.0;
    final bottomSafeArea = Platform.isAndroid
        ? MediaQuery.of(context).viewPadding.bottom
        : 0.0;
    final topSafeArea = (!hasOverlayElements && Platform.isAndroid)
        ? MediaQuery.of(context).viewPadding.top + kToolbarHeight
        : 0.0;
    final visibleMapHeight = mapHeight - bottomPadding - bottomSafeArea;
    final mapCenterY = visibleMapHeight / 2;

    // ✅ Context-aware pin positioning
    final pinTopPosition = _calculatePinPosition(
      mapCenterY: mapCenterY,
      pinHeight: pinHeight,
      hasOverlayElements: hasOverlayElements,
      topSafeArea: topSafeArea,
    );
    return Container(
      color: const Color.fromARGB(255, 227, 227, 229),
      child: (currentLocation == null)
          ? Center(child: CircularProgressIndicator())
          : Stack(
              children: [
                ClipRRect(
                  child: GoogleMap(
                    padding: EdgeInsets.only(bottom: bottomPadding),
                    initialCameraPosition: CameraPosition(
                      target: currentLocation!,
                      zoom: LocationConstants.zoomLevel,
                    ),
                    myLocationEnabled: isUserLocationEnabled,
                    myLocationButtonEnabled: false,
                    zoomControlsEnabled: false,
                    onMapCreated: onMapCreated,
                    onCameraMove: onCameraMove,
                    onCameraIdle: onCameraIdle,
                  ),
                ),

                Positioned(
                  left: 0,
                  right: 0,
                  // top: (mapHeight - bottomPadding) / 2 - pinHeight,
                  top: pinTopPosition,
                  child: Center(
                    child: Image.asset(
                      AssetPaths.selectPin,
                      width: pinHeight,
                      height: pinHeight,
                    ),
                  ),
                ),
                if (isFromFav) _buildSearchButton(),
              ],
            ),
    );
  }

  Widget _buildSearchButton() {
    return Positioned(
      top: 20,
      left: 20,
      right: 20,
      child: SearchLocationFieldButton(searchTapped: searchTapped!),
    );
  }

  // ✅ Context-aware pin calculation
  double _calculatePinPosition({
    required double mapCenterY,
    required double pinHeight,
    required bool hasOverlayElements,
    required double topSafeArea,
  }) {
    // if (Platform.isAndroid) {
    // if (hasOverlayElements) {
    //   // ConfirmPickupScreen has nav button + search bar overlays
    //   return mapCenterY - (pinHeight * 0.6); // Less adjustment needed
    // } else {
    //   // AddFavouriteLocationScreen - simpler layout
    //   return mapCenterY - (pinHeight * 0.75); // More adjustment needed
    // }
    // } else {
    // iOS - your working calculation
    if (!hasOverlayElements && Platform.isAndroid) {
      return topSafeArea + (mapCenterY - pinHeight);
    }
    return mapCenterY - pinHeight;
    // }
  }
}
