import 'package:tukxi/features/booking/data/models/driver.dart';
import 'package:tukxi/features/booking/data/models/product.dart';
import 'package:tukxi/features/booking/domain/entities/ride_entity.dart';
import 'package:tukxi/features/favourite_locations/data/models/location_response.dart';

class Ride extends RideEntity {
  Ride({
    required super.id,
    required super.driverId,
    required super.riderId,
    required super.productId,
    required super.status,
    required super.pickupLocation,
    required super.destinationLocation,
    required super.stops,
    required super.verificationCode,
    required super.createdAt,
    required super.updatedAt,
    super.completedAt,
    required super.driver,
    required super.product,
    required super.rideLifecycles,
    super.rider,
    super.vehicle,
  });

  factory Ride.fromJson(Map<String, dynamic> json) => Ride(
    id: json['id'] as String? ?? '',
    driverId: json['driverId'] as String? ?? '',
    riderId: json['riderId'] as String? ?? '',
    productId: json['productId'] as String? ?? '',
    status: json['status'] as String? ?? '',
    pickupLocation: LocationResponse.fromJson(json['pickupLocation']),
    destinationLocation: LocationResponse.fromJson(json['destinationLocation']),
    stops: json['stops'] == null
        ? []
        : (json['stops'] as List)
              .map((e) => LocationResponse.fromJson(e))
              .toList(),
    verificationCode: json['verificationCode'] as int?,
    createdAt: DateTime.parse(json['createdAt']),
    updatedAt: DateTime.parse(json['updatedAt']),
    completedAt: json['completedAt'] != null
        ? DateTime.parse(json['completedAt'])
        : null,
    driver: json['driver'] == null ? null : Driver.fromJson(json['driver']),
    product: json['product'] == null ? null : Product.fromJson(json['product']),
    rideLifecycles: json['rideLifecycles'] == null
        ? []
        : (json['rideLifecycles'] as List)
              .map((e) => RideLifecycle.fromJson(e))
              .toList(),
    rider: json['rider'] == null ? null : Rider.fromJson(json['rider']),
    vehicle: json['driverVehicle'] == null
        ? null
        : DriverVehicle.fromJson(json['driverVehicle']),
  );

  Map<String, dynamic> toJson() => {
    'id': id,
    'driverId': driverId,
    'riderId': riderId,
    'productId': productId,
    'status': status,
    'pickupLocation': pickupLocation?.toJson(),
    'destinationLocation': destinationLocation?.toJson(),
    'stops': stops?.map((e) => e.toJson()).toList(),
    'verificationCode': verificationCode,
    'createdAt': createdAt?.toIso8601String(),
    'updatedAt': updatedAt?.toIso8601String(),
    'completedAt': completedAt?.toIso8601String(),
    'driver': driver?.toJson(),
    'product': product?.toJson(),
    'rideLifecycles': rideLifecycles?.map((e) => e.toJson()).toList(),
    'rider': rider?.toJson(),
    'driverVehicle': vehicle?.toJson(),
  };
}

class Rider extends RiderEntity {
  Rider({required super.id, required super.rideOtp, super.rating});

  factory Rider.fromJson(Map<String, dynamic> json) => Rider(
    id: json['id'] as String?,
    rideOtp: json['rideOtp'] as String?,
    rating: json['rating'] == null
        ? null
        : (json['rating'] as num?)?.toDouble(),
  );

  Map<String, dynamic> toJson() => {
    'id': id,
    'rideOtp': rideOtp,
    'rating': rating,
  };
}

class DriverVehicle extends DriverVehicleEntity {
  DriverVehicle({required super.vehicleNumber, required super.vehicleType});

  factory DriverVehicle.fromJson(Map<String, dynamic> json) => DriverVehicle(
    vehicleNumber: json['vehicleNumber'] as String?,
    vehicleType: json['vehicleType'] == null
        ? null
        : VehicleType.fromJson(json['vehicleType']),
  );

  Map<String, dynamic> toJson() => {
    'vehicleNumber': vehicleNumber,
    'vehicleType': vehicleType?.toJson(),
  };
}

class VehicleType extends VehicleTypeEntity {
  VehicleType({required super.name});

  factory VehicleType.fromJson(Map<String, dynamic> json) =>
      VehicleType(name: json['name'] as String?);

  Map<String, dynamic> toJson() => {'name': name};
}

class RideLifecycle extends RideLifecycleEntity {
  RideLifecycle({
    required super.id,
    required super.status,
    required super.meta,
    required super.createdAt,
  });

  factory RideLifecycle.fromJson(Map<String, dynamic> json) => RideLifecycle(
    id: json['id'],
    status: json['status'],
    meta: json['meta'] == null ? null : json['meta'] as Map<String, dynamic>?,
    createdAt: DateTime.parse(json['createdAt']),
  );

  Map<String, dynamic> toJson() => {
    'id': id,
    'status': status,
    'meta': meta,
    'createdAt': createdAt?.toIso8601String(),
  };
}
