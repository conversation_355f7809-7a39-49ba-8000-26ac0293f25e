'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface ChargeDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading: boolean;
  chargeName: string;
}

export function ChargeDeleteModal({
  isOpen,
  onClose,
  onConfirm,
  isLoading,
  chargeName,
}: ChargeDeleteModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className='max-w-md'>
        <DialogHeader>
          <DialogTitle>Delete Charge</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete this charge? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>

        <div className='py-4'>
          <p className='text-sm text-gray-700'>
            You are about to delete: <span className='font-semibold'>{chargeName}</span>
          </p>
          <p className='text-sm text-red-600 mt-2'>
            Warning: If this charge is referenced by other charges, the deletion will fail.
          </p>
        </div>

        <div className='flex gap-3'>
          <Button
            type='button'
            variant='outline'
            onClick={onClose}
            disabled={isLoading}
            className='flex-1'
          >
            Cancel
          </Button>
          <Button
            type='button'
            variant='destructive'
            onClick={onConfirm}
            disabled={isLoading}
            className='flex-1'
          >
            {isLoading ? (
              <>
                Deleting...
                <Spinner className='ml-2 h-4 w-4' />
              </>
            ) : (
              'Delete'
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}