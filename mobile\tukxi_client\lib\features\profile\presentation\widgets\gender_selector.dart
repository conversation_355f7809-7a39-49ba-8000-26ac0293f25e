import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/enums/enum_mappers.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/utils/device_utils.dart';

class GenderSelector extends StatefulWidget {
  const GenderSelector({
    super.key,
    required this.selectedGender,
    required this.onChanged,
    required this.padding,
    this.errorMessage,
  });

  final Gender? selectedGender;
  final ValueChanged<Gender> onChanged;
  final double padding;
  final String? errorMessage;

  @override
  State<GenderSelector> createState() {
    return _GenderSelectorState();
  }
}

class _GenderSelectorState extends State<GenderSelector> {
  bool _isTablet = false;
  Gender? selectedGender;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;
      final isTablet = await DeviceUtils.isTablet(context);
      setState(() {
        _isTablet = isTablet;
        selectedGender = widget.selectedGender;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final spacing = 10.0;
    final availableWidth = width - widget.padding - spacing;
    final cardWidth = _isTablet ? 100.0 : availableWidth / 2;
    return Center(
      child: Column(
        children: [
          Wrap(
            spacing: spacing,
            runSpacing: 16,
            alignment: _isTablet ? WrapAlignment.center : WrapAlignment.start,
            children: genders.map((gender) {
              final isSelected = gender == selectedGender;
              return InkWell(
                onTap: () {
                  setState(() {
                    selectedGender = gender;
                  });
                  widget.onChanged(gender);
                },
                child: Container(
                  width: cardWidth,
                  height: 54,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: AppColors.greyBg,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected
                          ? AppColors.primary
                          : Colors.transparent,
                      width: 2,
                    ),
                  ),
                  child: Text(
                    gender.value,
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),

          if (widget.errorMessage != null) ...[
            const SizedBox(height: 8),
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                widget.errorMessage!,
                style: GoogleFonts.inter(
                  fontSize: 12,
                  color: AppColors.red,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
