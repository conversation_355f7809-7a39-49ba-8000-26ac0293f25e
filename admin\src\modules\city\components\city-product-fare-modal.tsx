'use client';

import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { Spinner } from '@/components/ui/spinner';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import {
   useCreateCityProductFare,
   useUpdateCityProductFare,
} from '../api/city-product-fare-mutations';
// import { useCityZones } from '../api/city-zone-queries';
import { useListZoneType } from '@/modules/zone-type/api/queries';
import { CityProductFare, CityProductFareStatus } from '../types/city-product-fare';

const fareSchema = z.object({
   priority: z.number().min(1, 'Priority must be at least 1'),
   status: z.nativeEnum(CityProductFareStatus),
   // fromZoneId: z.string().optional(),
   // toZoneId: z.string().optional(),
   fromZoneTypeId: z.string().optional(),
   toZoneTypeId: z.string().optional(),
});

type FareFormData = z.infer<typeof fareSchema>;

interface CityProductFareModalProps {
   cityProductId: string;
   cityId: string;
   fare?: CityProductFare | null;
   open: boolean;
   onOpenChange: (open: boolean) => void;
}

export function CityProductFareModal({
   cityProductId,
   fare,
   open,
   onOpenChange,
}: CityProductFareModalProps) {
   const isEditMode = !!fare;
   const queryClient = useQueryClient();

   const createMutation = useCreateCityProductFare();
   const updateMutation = useUpdateCityProductFare();
   // const cityZonesQuery = useCityZones(cityId, { includeRelations: false });
   const zoneTypesQuery = useListZoneType({
      page: 1,
      limit: 100,
      includeInactive: false,
   });

   const {
      register,
      handleSubmit,
      formState: { errors },
      reset,
      setValue,
      watch,
   } = useForm<FareFormData>({
      resolver: zodResolver(fareSchema),
      defaultValues: {
         priority: 1,
         status: CityProductFareStatus.ACTIVE,
         // fromZoneId: undefined,
         // toZoneId: undefined,
         fromZoneTypeId: undefined,
         toZoneTypeId: undefined,
      },
   });

   const statusValue = watch('status');
   // const fromZoneValue = watch('fromZoneId');
   // const toZoneValue = watch('toZoneId');
   const fromZoneTypeValue = watch('fromZoneTypeId');
   const toZoneTypeValue = watch('toZoneTypeId');

   // Reset form when modal opens or fare changes
   useEffect(() => {
      if (open) {
         if (fare) {
            reset({
               priority: fare.priority,
               status: fare.status,
               // When editing, the API returns fromZoneId/toZoneId, but we display zone types
               // So we keep the fields empty for now (will need backend to return zone type IDs)
               fromZoneTypeId: undefined,
               toZoneTypeId: undefined,
            });
         } else {
            reset({
               priority: 1,
               status: CityProductFareStatus.ACTIVE,
               fromZoneTypeId: undefined,
               toZoneTypeId: undefined,
            });
         }
      }
   }, [open, fare, reset]);

   const onSubmit = async (data: FareFormData) => {
      try {
         if (isEditMode && fare) {
            await updateMutation.mutateAsync({
               fareId: fare.id,
               data: {
                  priority: data.priority,
                  status: data.status,
                  // fromZoneId: data.fromZoneId || undefined,
                  // toZoneId: data.toZoneId || undefined,
                  fromZoneTypeId: data.fromZoneTypeId || undefined,
                  toZoneTypeId: data.toZoneTypeId || undefined,
               },
            });
            toast.success('Fare updated successfully');
         } else {
            await createMutation.mutateAsync({
               cityProductId,
               priority: data.priority,
               status: data.status,
               // fromZoneId: data.fromZoneId || undefined,
               // toZoneId: data.toZoneId || undefined,
               fromZoneTypeId: data.fromZoneTypeId || undefined,
               toZoneTypeId: data.toZoneTypeId || undefined,
            });
            toast.success('Fare created successfully');
         }
         queryClient.invalidateQueries({
            queryKey: ['city-product-fares', cityProductId],
         });
         onOpenChange(false);
      } catch (error: any) {
         console.error(error);
      }
   };

   const isSubmitting = createMutation.isPending || updateMutation.isPending;
   const zoneTypes = zoneTypesQuery.data?.data.data || [];

   return (
      <Dialog open={open} onOpenChange={onOpenChange}>
         <DialogContent className='sm:max-w-[500px]'>
            <DialogHeader>
               <DialogTitle>{isEditMode ? 'Edit Fare' : 'Create New Fare'}</DialogTitle>
               <DialogDescription>
                  {isEditMode
                     ? 'Update the fare rule details'
                     : 'Create a new fare rule for this city product'}
               </DialogDescription>
            </DialogHeader>

            <form onSubmit={handleSubmit(onSubmit)}>
               <div className='space-y-4 py-4'>
                  {!isEditMode && (
                     <>
                        {/* Priority - Only shown when creating */}
                        <div className='space-y-2'>
                           <Label htmlFor='priority'>
                              Priority <span className='text-red-500'>*</span>
                           </Label>
                           <Input
                              id='priority'
                              type='number'
                              min='1'
                              {...register('priority', { valueAsNumber: true })}
                              placeholder='Enter priority (e.g., 1, 2, 3...)'
                           />
                           {errors.priority && (
                              <p className='text-sm text-red-500'>{errors.priority.message}</p>
                           )}
                           <p className='text-xs text-gray-500'>
                              Lower priority values are evaluated first
                           </p>
                        </div>

                        {/* From Zone Type - Only shown when creating */}
                        <div className='space-y-2'>
                           <Label htmlFor='fromZoneType'>From Zone Type</Label>
                           {zoneTypesQuery.isLoading ? (
                              <div className='flex items-center justify-center py-4'>
                                 <Spinner className='h-6 w-6' />
                              </div>
                           ) : (
                              <Select
                                 value={fromZoneTypeValue || 'none'}
                                 onValueChange={value =>
                                    setValue('fromZoneTypeId', value === 'none' ? undefined : value)
                                 }
                              >
                                 <SelectTrigger id='fromZoneType' className='w-full'>
                                    <SelectValue placeholder='Select from zone type' />
                                 </SelectTrigger>
                                 <SelectContent>
                                    <SelectItem value='none'>Any Zone Type</SelectItem>
                                    {zoneTypes.map(zoneType => (
                                       <SelectItem key={zoneType.id} value={zoneType.id}>
                                          {zoneType.name}
                                       </SelectItem>
                                    ))}
                                 </SelectContent>
                              </Select>
                           )}
                           <p className='text-xs text-gray-500'>
                              Leave as "Any Zone Type" for default fare rule
                           </p>
                        </div>

                        {/* To Zone Type - Only shown when creating */}
                        <div className='space-y-2'>
                           <Label htmlFor='toZoneType'>To Zone Type</Label>
                           {zoneTypesQuery.isLoading ? (
                              <div className='flex items-center justify-center py-4'>
                                 <Spinner className='h-6 w-6' />
                              </div>
                           ) : (
                              <Select
                                 value={toZoneTypeValue || 'none'}
                                 onValueChange={value =>
                                    setValue('toZoneTypeId', value === 'none' ? undefined : value)
                                 }
                              >
                                 <SelectTrigger id='toZoneType' className='w-full'>
                                    <SelectValue placeholder='Select to zone type' />
                                 </SelectTrigger>
                                 <SelectContent>
                                    <SelectItem value='none'>Any Zone Type</SelectItem>
                                    {zoneTypes.map(zoneType => (
                                       <SelectItem key={zoneType.id} value={zoneType.id}>
                                          {zoneType.name}
                                       </SelectItem>
                                    ))}
                                 </SelectContent>
                              </Select>
                           )}
                           <p className='text-xs text-gray-500'>
                              Leave as "Any Zone Type" for default fare rule
                           </p>
                        </div>
                     </>
                  )}

                  {/* Status - Always shown */}
                  <div className='space-y-2'>
                     <Label htmlFor='status'>
                        Status <span className='text-red-500'>*</span>
                     </Label>
                     <Select
                        value={statusValue}
                        onValueChange={value => setValue('status', value as CityProductFareStatus)}
                     >
                        <SelectTrigger id='status' className='w-full'>
                           <SelectValue placeholder='Select status' />
                        </SelectTrigger>
                        <SelectContent>
                           <SelectItem value={CityProductFareStatus.ACTIVE}>Active</SelectItem>
                           <SelectItem value={CityProductFareStatus.INACTIVE}>Inactive</SelectItem>
                        </SelectContent>
                     </Select>
                     {errors.status && (
                        <p className='text-sm text-red-500'>{errors.status.message}</p>
                     )}
                  </div>

                  {/* COMMENTED OUT - From Zone (Optional) - May be needed in the future
                 <div className='space-y-2'>
                    <Label htmlFor='fromZone'>From Zone (Optional)</Label>
                    {cityZonesQuery.isLoading ? (
                       <div className='flex items-center justify-center py-4'>
                          <Spinner className='h-6 w-6' />
                       </div>
                    ) : (
                       <Select
                          value={fromZoneValue || 'none'}
                          onValueChange={value =>
                             setValue('fromZoneId', value === 'none' ? undefined : value)
                          }
                          disabled={isPrimaryFare}
                       >
                          <SelectTrigger id='fromZone' className='w-full'>
                             <SelectValue placeholder='Select from zone' />
                          </SelectTrigger>
                          <SelectContent>
                             <SelectItem value='none'>Any Zone</SelectItem>
                             {zones.map(zone => (
                                <SelectItem key={zone.id} value={zone.id}>
                                   {zone.name}
                                </SelectItem>
                             ))}
                          </SelectContent>
                       </Select>
                    )}
                    <p className='text-xs text-gray-500'>
                       {isPrimaryFare
                          ? 'Cannot change zones for primary fare'
                          : 'Leave as "Any Zone" for default fare rule'}
                    </p>
                 </div>
                 */}

                  {/* COMMENTED OUT - To Zone (Optional) - May be needed in the future
                 <div className='space-y-2'>
                    <Label htmlFor='toZone'>To Zone (Optional)</Label>
                    {cityZonesQuery.isLoading ? (
                       <div className='flex items-center justify-center py-4'>
                          <Spinner className='h-6 w-6' />
                       </div>
                    ) : (
                       <Select
                          value={toZoneValue || 'none'}
                          onValueChange={value =>
                             setValue('toZoneId', value === 'none' ? undefined : value)
                          }
                          disabled={isPrimaryFare}
                       >
                          <SelectTrigger id='toZone' className='w-full'>
                             <SelectValue placeholder='Select to zone' />
                          </SelectTrigger>
                          <SelectContent>
                             <SelectItem value='none'>Any Zone</SelectItem>
                             {zones.map(zone => (
                                <SelectItem key={zone.id} value={zone.id}>
                                   {zone.name}
                                </SelectItem>
                             ))}
                          </SelectContent>
                       </Select>
                    )}
                    <p className='text-xs text-gray-500'>
                       {isPrimaryFare
                          ? 'Cannot change zones for primary fare'
                          : 'Leave as "Any Zone" for default fare rule'}
                    </p>
                 </div>
                 */}
               </div>

               <DialogFooter>
                  <Button
                     type='button'
                     variant='outline'
                     onClick={() => onOpenChange(false)}
                     disabled={isSubmitting}
                  >
                     Cancel
                  </Button>
                  <Button type='submit' disabled={isSubmitting}>
                     {isSubmitting ? (
                        <div className='flex items-center gap-2'>
                           <Spinner size='sm' />
                           {isEditMode ? 'Updating...' : 'Creating...'}
                        </div>
                     ) : isEditMode ? (
                        'Update Fare'
                     ) : (
                        'Create Fare'
                     )}
                  </Button>
               </DialogFooter>
            </form>
         </DialogContent>
      </Dialog>
   );
}
