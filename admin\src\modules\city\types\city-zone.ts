// Zone-related TypeScript interfaces for City Zones feature

export interface Zone {
  id: string;
  name: string;
  description?: string | null;
  zoneTypeId: string;
  cityId: string;
  polygon?: LatLng[] | null;
  meta?: {
    color?: string;
    zoomLevel?: string;
  } | null;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
  // Related data
  zoneType?: {
    id: string;
    name: string;
    algorithm: string;
  };
}

export interface LatLng {
  lat: number;
  lng: number;
}

// Request types for zone operations
export interface CreateZoneRequest {
  name: string;
  description?: string;
  zoneTypeId: string;
  cityId: string;
  polygon?: LatLng[] | null;
  meta?: {
    color?: string;
    zoomLevel?: string;
  };
}

export interface UpdateZoneRequest {
  name?: string;
  description?: string;
  zoneTypeId?: string;
  polygon?: LatLng[] | null;
  meta?: {
    color?: string;
    zoomLevel?: string;
  };
}

// API Response types
export interface ZoneResponse {
  success: boolean;
  message: string;
  data: Zone;
  timestamp: number;
}

export interface ListZoneParams {
  page?: number;
  limit?: number;
  search?: string;
  zoneTypeId?: string;
  includeRelations?: boolean;
}

export interface ListZoneResponse {
  success: boolean;
  message: string;
  data: {
    data: Zone[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  timestamp: number;
}

export interface ZoneCreateResponse {
  success: boolean;
  message: string;
  data: Zone;
  timestamp: number;
}

export interface ZoneUpdateResponse {
  success: boolean;
  message: string;
  data: Zone;
  timestamp: number;
}

export interface ZoneDeleteResponse {
  success: boolean;
  message: string;
  data: {
    id: string;
    deleted: boolean;
  };
  timestamp: number;
}

// City zones by city response (from /zones/city/:cityId endpoint)
export interface CityZonesResponse {
  success: boolean;
  message: string;
  data: Zone[];
  timestamp: number;
}